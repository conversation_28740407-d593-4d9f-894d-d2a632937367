<?php
// Include the shared header component
$pageTitle = "Billboard Designer - Borges Media";
$headerTitle = "Billboard Designer";
$headerSubtitle = "Create your perfect billboard design";
$headerIcon = "fas fa-paint-brush";
$additionalCSS = [
    "assets/css/mobile-first-styles.css",
    "assets/css/fabric-canvas-styles.css",
    "assets/css/mobile-toolbar-styles.css",
    "assets/css/responsive-modal-styles.css",
    "../shared/checkout-modal.css"
];
include '../shared/header.php';
?>

<div class="mobile-billboard-editor" id="mobileEditor">
    <!-- Desktop Left Sidebar - Design Tools -->
    <div class="desktop-left-sidebar" id="desktopLeftSidebar">
        <div class="sidebar-header">
            <h3 class="sidebar-title">Design Tools</h3>
        </div>
        <div class="sidebar-content" id="leftSidebarContent">
            <!-- Design tools will be moved here via JavaScript for desktop -->
        </div>
    </div>

    <!-- Canvas with Proper Wrapper for Responsive Scaling -->
    <div class="canvas-section">
        <div class="canvas-wrapper" id="canvasWrapper">
            <canvas id="fabricCanvas" class="fabric-canvas"></canvas>
        </div>
        <div class="canvas-overlay" id="canvasOverlay">
            <div class="canvas-loading" id="canvasLoading">
                <div class="loading-spinner"></div>
                <span>Loading Canvas...</span>
            </div>
        </div>
    </div>

    <!-- Desktop Right Sidebar - Export & Checkout -->
    <div class="desktop-right-sidebar" id="desktopRightSidebar">
        <div class="sidebar-header">
            <h3 class="sidebar-title">Export & Checkout</h3>
        </div>
        <div class="sidebar-content" id="rightSidebarContent">
            <!-- Export tools will be moved here via JavaScript for desktop -->
        </div>
    </div>

    <!-- Mobile-First Toolbar (Hidden on Desktop) -->
    <div class="mobile-toolbar" id="mobileToolbar">
        <!-- Toolbar Header -->
        <div class="toolbar-header">
            <h3 class="toolbar-title">Design Tools</h3>
            <button class="toolbar-toggle" id="toolbarToggle" aria-label="Toggle toolbar">
                <i class="fas fa-chevron-up"></i>
            </button>
        </div>

        <!-- Toolbar Content -->
        <div class="toolbar-content" id="toolbarContent">
            <!-- Background Tools Section -->
            <div class="tool-section" id="backgroundSection">
                <div class="section-header">
                    <h4 class="section-title">Background</h4>
                    <button class="section-toggle" data-target="backgroundTools" aria-label="Toggle background tools">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                <div class="section-content" id="backgroundTools">
                    <div class="tool-grid">
                        <button class="tool-btn primary" id="chooseBackground" aria-label="Choose background">
                            <i class="fas fa-image"></i>
                            <span>Choose Background</span>
                        </button>
                        <button class="tool-btn secondary" id="clearBackground" aria-label="Clear background">
                            <i class="fas fa-times"></i>
                            <span>Clear Background</span>
                        </button>
                        <button class="tool-btn secondary" id="addImage" aria-label="Add image">
                            <i class="fas fa-plus"></i>
                            <span>Add Image</span>
                        </button>
                        <button class="tool-btn danger" id="clearAll" aria-label="Clear all">
                            <i class="fas fa-trash"></i>
                            <span>Clear All</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Text Tools Section -->
            <div class="tool-section" id="textSection">
                <div class="section-header">
                    <h4 class="section-title">Text</h4>
                    <button class="section-toggle" data-target="textTools" aria-label="Toggle text tools">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                <div class="section-content" id="textTools">
                    <div class="tool-grid">
                        <button class="tool-btn primary" id="addText" aria-label="Add text">
                            <i class="fas fa-font"></i>
                            <span>Add Text</span>
                        </button>
                    </div>
                    
                    <!-- Text Properties Panel -->
                    <div class="text-properties" id="textProperties" style="display: none;">
                        <!-- Font Family -->
                        <div class="property-group">
                            <label for="fontFamily" class="property-label">Font Family</label>
                            <select id="fontFamily" class="property-select">
                                <option value="Inter">Inter</option>
                                <option value="Roboto">Roboto</option>
                                <option value="Montserrat">Montserrat</option>
                                <option value="Open Sans">Open Sans</option>
                                <option value="Lato">Lato</option>
                                <option value="Poppins">Poppins</option>
                            </select>
                        </div>

                        <!-- Font Size -->
                        <div class="property-group">
                            <label for="fontSize" class="property-label">Font Size</label>
                            <div class="input-with-controls">
                                <input type="number" id="fontSize" class="property-input" min="8" max="200" value="24">
                                <div class="input-controls">
                                    <button class="input-btn" id="fontSizeDown" aria-label="Decrease font size">-</button>
                                    <button class="input-btn" id="fontSizeUp" aria-label="Increase font size">+</button>
                                </div>
                            </div>
                        </div>

                        <!-- Text Style Controls -->
                        <div class="property-group">
                            <label class="property-label">Text Style</label>
                            <div class="toggle-group">
                                <button class="toggle-btn" id="boldToggle" aria-label="Bold">
                                    <i class="fas fa-bold"></i>
                                </button>
                                <button class="toggle-btn" id="italicToggle" aria-label="Italic">
                                    <i class="fas fa-italic"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Text Alignment -->
                        <div class="property-group">
                            <label class="property-label">Alignment</label>
                            <div class="toggle-group">
                                <button class="toggle-btn" id="alignLeft" data-align="left" aria-label="Align left">
                                    <i class="fas fa-align-left"></i>
                                </button>
                                <button class="toggle-btn" id="alignCenter" data-align="center" aria-label="Align center">
                                    <i class="fas fa-align-center"></i>
                                </button>
                                <button class="toggle-btn" id="alignRight" data-align="right" aria-label="Align right">
                                    <i class="fas fa-align-right"></i>
                                </button>
                                <button class="toggle-btn" id="alignJustify" data-align="justify" aria-label="Justify">
                                    <i class="fas fa-align-justify"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Font Color -->
                        <div class="property-group">
                            <label for="fontColor" class="property-label">Font Color</label>
                            <div class="color-picker-wrapper">
                                <input type="color" id="fontColor" class="color-picker" value="#000000">
                                <div class="color-preview" id="fontColorPreview"></div>
                            </div>
                        </div>

                        <!-- Text Shadow Section -->
                        <div class="property-group">
                            <div class="property-header">
                                <label class="property-label">Text Shadow</label>
                                <button class="toggle-btn shadow-toggle" id="shadowToggle" aria-label="Toggle shadow">
                                    <span>S</span>
                                </button>
                            </div>
                            
                            <div class="shadow-controls" id="shadowControls" style="display: none;">
                                <!-- Shadow Color -->
                                <div class="sub-property">
                                    <label for="shadowColor" class="sub-label">Shadow Color</label>
                                    <div class="color-picker-wrapper">
                                        <input type="color" id="shadowColor" class="color-picker" value="#000000">
                                        <div class="color-preview" id="shadowColorPreview"></div>
                                    </div>
                                </div>

                                <!-- Shadow Type Selector -->
                                <div class="sub-property">
                                    <label for="shadowType" class="sub-label">Shadow Type</label>
                                    <select id="shadowType" class="property-select">
                                        <option value="glow">Glow (No Offset)</option>
                                        <option value="drop">Drop Shadow</option>
                                    </select>
                                </div>

                                <!-- Shadow Blur -->
                                <div class="sub-property">
                                    <label for="shadowBlur" class="sub-label">Blur</label>
                                    <input type="range" id="shadowBlur" class="range-slider" min="1" max="30" value="5">
                                    <span class="range-value" id="shadowBlurValue">5</span>
                                </div>

                                <!-- Shadow Offset Controls (only for drop shadow) -->
                                <div id="offsetControls" style="display: none;">
                                    <!-- Shadow Offset X -->
                                    <div class="sub-property">
                                        <label for="shadowOffsetX" class="sub-label">Offset X</label>
                                        <input type="range" id="shadowOffsetX" class="range-slider" min="-20" max="20" value="2">
                                        <span class="range-value" id="shadowOffsetXValue">2</span>
                                    </div>

                                    <!-- Shadow Offset Y -->
                                    <div class="sub-property">
                                        <label for="shadowOffsetY" class="sub-label">Offset Y</label>
                                        <input type="range" id="shadowOffsetY" class="range-slider" min="-20" max="20" value="2">
                                        <span class="range-value" id="shadowOffsetYValue">2</span>
                                    </div>
                                </div>

                                <!-- Shadow Opacity -->
                                <div class="sub-property">
                                    <label for="shadowOpacity" class="sub-label">Opacity</label>
                                    <input type="range" id="shadowOpacity" class="range-slider" min="0" max="100" value="100">
                                    <span class="range-value" id="shadowOpacityValue">100%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Export Tools Section -->
            <div class="tool-section" id="exportSection">
                <div class="section-header">
                    <h4 class="section-title">Export & Checkout</h4>
                    <button class="section-toggle" data-target="exportTools" aria-label="Toggle export tools">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                <div class="section-content" id="exportTools">
                    <!-- Export Quality -->
                    <div class="property-group">
                        <label for="exportQuality" class="property-label">Export Quality</label>
                        <select id="exportQuality" class="property-select">
                            <option value="web">Web (1x - ~500KB)</option>
                            <option value="standard" selected>Standard (2x - ~2MB)</option>
                            <option value="high">High (3x - ~5MB)</option>
                            <option value="ultra">Ultra HD (4x - ~8MB)</option>
                            <option value="super">Super HD (6x - ~13MB)</option>
                        </select>
                    </div>

                    <!-- Export Format -->
                    <div class="property-group">
                        <label for="exportFormat" class="property-label">Export Format</label>
                        <select id="exportFormat" class="property-select">
                            <option value="png" selected>PNG</option>
                            <option value="jpeg">JPEG</option>
                        </select>
                    </div>

                    <!-- Export Actions -->
                    <div class="tool-grid">
                        <button class="tool-btn primary" id="downloadImage" aria-label="Download image">
                            <i class="fas fa-download"></i>
                            <span>Download Image</span>
                        </button>
                        <button class="tool-btn success" id="checkoutBtn" aria-label="Proceed to checkout">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Checkout</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Background Template Modal -->
<div class="modal-overlay" id="backgroundModal" style="display: none;">
    <div class="modal-container">
        <div class="modal-header">
            <h3 class="modal-title" id="modalTitle">Choose Background</h3>
            <button class="modal-close" id="modalClose" aria-label="Close modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-content" id="modalContent">
            <!-- Step 1: Category Selection -->
            <div class="modal-step" id="categoryStep">
                <div class="category-grid" id="categoryGrid">
                    <!-- Categories will be populated dynamically -->
                </div>
            </div>

            <!-- Step 2: Template Selection -->
            <div class="modal-step" id="templateStep" style="display: none;">
                <div class="template-grid" id="templateGrid">
                    <!-- Templates will be populated dynamically -->
                </div>
            </div>
        </div>

        <div class="modal-footer">
            <button class="modal-btn secondary" id="modalBack" style="display: none;">
                <i class="fas fa-arrow-left"></i>
                <span>Back</span>
            </button>
            <button class="modal-btn secondary" id="modalCancel">
                <span>Cancel</span>
            </button>
            <button class="modal-btn primary" id="modalApply" disabled>
                <span>Apply Background</span>
            </button>
        </div>
    </div>
</div>

<!-- Hidden file input for image upload -->
<input type="file" id="imageUpload" accept="image/*" style="display: none;">

<!-- Checkout modal will be included at the end -->

<!-- Fabric.js Library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>

<!-- Simple Initialization Script -->
<script>
console.log('🔄 Starting Billboard Editor initialization...');

// Simple canvas initialization that bypasses complex wrapper
function initializeSimpleCanvas() {
    try {
        console.log('🔄 Checking Fabric.js availability...');
        if (typeof fabric === 'undefined') {
            throw new Error('Fabric.js not loaded');
        }
        console.log('✅ Fabric.js available (version: ' + fabric.version + ')');

        // Hide loading overlay
        const overlay = document.getElementById('canvasOverlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }

        // Create simple canvas directly
        console.log('🔄 Creating canvas...');
        const canvas = new fabric.Canvas('fabricCanvas', {
            width: 800,
            height: 400,
            backgroundColor: '#ffffff',
            selection: true,
            preserveObjectStacking: true,
            enableRetinaScaling: true
        });

        // Add thick black border to canvas only
        const canvasElement = document.getElementById('fabricCanvas');

        if (canvasElement) {
            canvasElement.style.border = '4px solid #000000';
            canvasElement.style.borderRadius = '8px';
            canvasElement.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
            canvasElement.style.display = 'block';
            canvasElement.style.margin = '0 auto';
            console.log('✅ Canvas styled directly without container');
        }

        if (!canvas) {
            throw new Error('Failed to create canvas');
        }

        console.log('✅ Canvas created successfully');

        // Setup responsive behavior - Fixed for desktop/mobile compatibility
        function resizeCanvas() {
            const canvasSection = document.querySelector('.canvas-section');
            if (!canvasSection || !canvas) return;

            // Get available space
            const sectionWidth = canvasSection.clientWidth - 40; // More padding for desktop
            const sectionHeight = canvasSection.clientHeight - 40;

            // Original canvas dimensions
            const originalWidth = 800;
            const originalHeight = 400;

            // Calculate scale to fit container while maintaining aspect ratio
            const scaleX = sectionWidth / originalWidth;
            const scaleY = sectionHeight / originalHeight;
            const scale = Math.min(scaleX, scaleY, 1); // Never scale up beyond 100%

            console.log('📐 Responsive canvas:', {
                sectionSize: `${sectionWidth}x${sectionHeight}`,
                scale: scale,
                finalSize: `${originalWidth * scale}x${originalHeight * scale}`
            });

            // Apply scaling using CSS transform instead of Fabric.js zoom
            // This prevents object distortion issues
            const canvasWrapper = document.getElementById('canvasWrapper');

            if (canvasWrapper && scale < 1) {
                // For smaller screens, use CSS transform scaling
                canvas.setDimensions({
                    width: originalWidth,
                    height: originalHeight
                });

                canvasWrapper.style.transform = `scale(${scale})`;
                canvasWrapper.style.transformOrigin = 'center center';
                canvasWrapper.style.width = `${originalWidth}px`;
                canvasWrapper.style.height = `${originalHeight}px`;

                console.log('✅ Applied CSS transform scaling for responsive display');
            } else {
                // For desktop/larger screens, keep original size
                canvas.setDimensions({
                    width: originalWidth,
                    height: originalHeight
                });

                if (canvasWrapper) {
                    canvasWrapper.style.transform = 'scale(1)';
                    canvasWrapper.style.width = `${originalWidth}px`;
                    canvasWrapper.style.height = `${originalHeight}px`;
                }

                console.log('✅ Canvas displayed at original size for desktop');
            }

            canvas.renderAll();
        }

        window.addEventListener('resize', resizeCanvas);
        setTimeout(resizeCanvas, 100);

        // Setup responsive layout switching
        setupResponsiveLayout();

        // Store canvas globally for toolbar access
        window.billboardCanvas = canvas;

        console.log('🎉 Billboard Editor initialized successfully!');

        // Initialize basic toolbar functionality
        initializeBasicToolbar(canvas);

        // Initialize checkout system
        initializeCheckoutSystem();

    } catch (error) {
        console.error('❌ Initialization failed:', error);
        const loadingElement = document.getElementById('canvasLoading');
        if (loadingElement) {
            loadingElement.innerHTML = `
                <div style="text-align: center; color: #dc2626;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <h3>Initialization Failed</h3>
                    <p>${error.message}</p>
                    <button onclick="window.location.reload()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #2563eb; color: white; border: none; border-radius: 0.5rem; cursor: pointer;">
                        <i class="fas fa-redo"></i> Retry
                    </button>
                </div>
            `;
        }
    }
}

// Basic toolbar functionality
function initializeBasicToolbar(canvas) {
    console.log('🔄 Setting up basic toolbar...');

    // Add delete controls and keyboard shortcuts
    function setupDeleteFunctionality() {
        // Configure object controls
        fabric.Object.prototype.set({
            borderColor: '#2563eb',
            cornerColor: '#ffffff',
            cornerStrokeColor: '#2563eb',
            cornerStyle: 'circle',
            cornerSize: 12,
            transparentCorners: false,
            hasControls: true,
            hasBorders: true
        });

        // Add custom delete control (X button)
        function addDeleteControl(obj) {
            const deleteIcon = "data:image/svg+xml,%3csvg%20width='100'%20height='100'%20xmlns='http://www.w3.org/2000/svg'%3e%3ctext%20x='50'%20y='50'%20font-size='50'%20text-anchor='middle'%20dy='0.35em'%20fill='%23ff0000'%3e×%3c/text%3e%3c/svg%3e";

            obj.controls.deleteControl = new fabric.Control({
                x: 0.5,
                y: -0.5,
                offsetY: -8,
                offsetX: 8,
                cursorStyle: 'pointer',
                mouseUpHandler: function(eventData, transform) {
                    const target = transform.target;
                    canvas.remove(target);
                    canvas.renderAll();

                    // Hide text properties if text was deleted
                    const textProperties = document.getElementById('textProperties');
                    if (textProperties && (target.type === 'text' || target.type === 'i-text')) {
                        textProperties.style.display = 'none';
                    }

                    console.log('✅ Object deleted with X button');
                },
                render: function(ctx, left, top, styleOverride, fabricObject) {
                    const size = 20;
                    ctx.save();
                    ctx.translate(left, top);
                    ctx.rotate(fabric.util.degreesToRadians(fabricObject.angle));
                    ctx.fillStyle = '#ff0000';
                    ctx.strokeStyle = '#ffffff';
                    ctx.lineWidth = 2;

                    // Draw circle background
                    ctx.beginPath();
                    ctx.arc(0, 0, size/2, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.stroke();

                    // Draw X
                    ctx.strokeStyle = '#ffffff';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(-6, -6);
                    ctx.lineTo(6, 6);
                    ctx.moveTo(6, -6);
                    ctx.lineTo(-6, 6);
                    ctx.stroke();

                    ctx.restore();
                },
                cornerSize: 20
            });
        }

        // Add delete control to all new objects
        canvas.on('object:added', function(e) {
            if (e.target && !e.target.isBackground) {
                addDeleteControl(e.target);
            }
        });

        // Delete key functionality
        document.addEventListener('keydown', (e) => {
            if ((e.key === 'Delete' || e.key === 'Backspace') && !e.target.matches('input, textarea')) {
                const activeObjects = canvas.getActiveObjects();
                if (activeObjects.length > 0) {
                    activeObjects.forEach(obj => {
                        canvas.remove(obj);
                    });
                    canvas.discardActiveObject();
                    canvas.renderAll();
                    console.log('✅ Objects deleted with keyboard');

                    // Hide text properties if text was deleted
                    const textProperties = document.getElementById('textProperties');
                    if (textProperties) {
                        textProperties.style.display = 'none';
                    }
                }
            }
        });

        // Right-click delete (but don't interfere with text editing)
        canvas.on('mouse:down', function(options) {
            if (options.e.button === 2) { // Right click
                options.e.preventDefault();
                const activeObject = canvas.getActiveObject();
                // Don't delete if text is in editing mode
                if (activeObject && !activeObject.isEditing) {
                    canvas.remove(activeObject);
                    canvas.renderAll();
                    console.log('✅ Object deleted with right-click');

                    // Hide text properties
                    const textProperties = document.getElementById('textProperties');
                    if (textProperties) {
                        textProperties.style.display = 'none';
                    }
                }
            }
        });

        // Text editing event handlers
        canvas.on('text:editing:entered', function(e) {
            console.log('✅ Text editing started');
            const textObj = e.target;
            if (textObj) {
                // Highlight the editing text
                textObj.set('editingBorderColor', '#2563eb');
                canvas.renderAll();
            }
        });

        canvas.on('text:editing:exited', function(e) {
            console.log('✅ Text editing finished');
            const textObj = e.target;
            if (textObj) {
                // Update text properties panel if visible
                updateTextPropertiesPanel(textObj);
                // Update button states after editing
                updateButtonStates();
                canvas.renderAll();
            }
        });

        // Double-click to edit text
        canvas.on('mouse:dblclick', function(options) {
            const target = options.target;
            if (target && (target.type === 'text' || target.type === 'i-text')) {
                target.enterEditing();
                target.selectAll();
                console.log('✅ Text editing activated via double-click');
            }
        });

        // Mobile touch handling for text editing
        let touchTimeout;
        let touchCount = 0;

        canvas.on('touch:gesture', function(e) {
            e.e.preventDefault();
        });

        canvas.on('mouse:down', function(options) {
            if (options.e.touches) { // Touch event
                touchCount++;

                if (touchCount === 1) {
                    touchTimeout = setTimeout(() => {
                        touchCount = 0;
                    }, 300);
                } else if (touchCount === 2) {
                    // Double tap detected
                    clearTimeout(touchTimeout);
                    touchCount = 0;

                    const target = options.target;
                    if (target && (target.type === 'text' || target.type === 'i-text')) {
                        target.enterEditing();
                        target.selectAll();
                        console.log('✅ Text editing activated via double-tap (mobile)');
                    }
                }
            }
        });

        // Prevent context menu
        canvas.upperCanvasEl.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });
    }

    setupDeleteFunctionality();

    // Choose Background functionality
    const chooseBackgroundBtn = document.getElementById('chooseBackground');
    const backgroundModal = document.getElementById('backgroundModal');
    const modalClose = document.getElementById('modalClose');
    const modalCancel = document.getElementById('modalCancel');

    if (chooseBackgroundBtn && backgroundModal) {
        chooseBackgroundBtn.addEventListener('click', () => {
            // Simple background selection - just show modal
            backgroundModal.style.display = 'flex';
            setTimeout(() => {
                backgroundModal.classList.add('show');
            }, 10);

            // Load basic templates if available
            loadBasicTemplates();
        });
    }

    if (modalClose) {
        modalClose.addEventListener('click', () => {
            closeBackgroundModal();
        });
    }

    if (modalCancel) {
        modalCancel.addEventListener('click', () => {
            closeBackgroundModal();
        });
    }

    // Modal back button
    const modalBack = document.getElementById('modalBack');
    if (modalBack) {
        modalBack.addEventListener('click', () => {
            // Go back to categories
            const categoryStep = document.getElementById('categoryStep');
            const templateStep = document.getElementById('templateStep');
            const modalTitle = document.getElementById('modalTitle');

            if (categoryStep) categoryStep.style.display = 'block';
            if (templateStep) templateStep.style.display = 'none';
            if (modalTitle) modalTitle.textContent = 'Choose Background Category';
            modalBack.style.display = 'none';
        });
    }

    // Close modal when clicking outside
    if (backgroundModal) {
        backgroundModal.addEventListener('click', (e) => {
            if (e.target === backgroundModal) {
                closeBackgroundModal();
            }
        });
    }

    // Escape key to close modal
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && backgroundModal && backgroundModal.style.display === 'flex') {
            closeBackgroundModal();
        }
    });

    function closeBackgroundModal() {
        if (backgroundModal) {
            backgroundModal.classList.remove('show');
            setTimeout(() => {
                backgroundModal.style.display = 'none';

                // Reset modal to category view
                const categoryStep = document.getElementById('categoryStep');
                const templateStep = document.getElementById('templateStep');
                const modalTitle = document.getElementById('modalTitle');
                const modalBack = document.getElementById('modalBack');

                if (categoryStep) categoryStep.style.display = 'block';
                if (templateStep) templateStep.style.display = 'none';
                if (modalTitle) modalTitle.textContent = 'Choose Background Category';
                if (modalBack) modalBack.style.display = 'none';
            }, 300);
        }
    }

    function loadBasicTemplates() {
        const categoryGrid = document.getElementById('categoryGrid');
        const templateGrid = document.getElementById('templateGrid');
        const categoryStep = document.getElementById('categoryStep');
        const templateStep = document.getElementById('templateStep');
        const modalTitle = document.getElementById('modalTitle');
        const modalBack = document.getElementById('modalBack');

        if (categoryGrid && typeof window.BACKGROUND_TEMPLATES !== 'undefined') {
            categoryGrid.innerHTML = '';

            const categories = Object.keys(window.BACKGROUND_TEMPLATES);
            console.log('📁 Loading categories:', categories);

            categories.forEach(category => { // Show ALL categories
                const categoryItem = document.createElement('div');
                categoryItem.className = 'category-item';
                categoryItem.innerHTML = `
                    <i class="fas fa-image"></i>
                    <span class="category-name">${category}</span>
                `;

                categoryItem.addEventListener('click', () => {
                    showCategoryTemplates(category);
                });

                categoryGrid.appendChild(categoryItem);
            });

            console.log('✅ Loaded', categories.length, 'background categories');
        } else {
            // Fallback if templates not loaded
            categoryGrid.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 2rem; color: #6b7280;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <p>Background templates not available</p>
                    <p style="font-size: 0.875rem; margin-top: 0.5rem;">Check console for loading errors</p>
                </div>
            `;
        }
    }

    function showCategoryTemplates(category) {
        const templates = window.BACKGROUND_TEMPLATES[category];
        const categoryStep = document.getElementById('categoryStep');
        const templateStep = document.getElementById('templateStep');
        const templateGrid = document.getElementById('templateGrid');
        const modalTitle = document.getElementById('modalTitle');
        const modalBack = document.getElementById('modalBack');
        const modalApply = document.getElementById('modalApply');

        if (templates && templates.length > 0) {
            // Switch to template view
            if (categoryStep) categoryStep.style.display = 'none';
            if (templateStep) templateStep.style.display = 'block';
            if (modalTitle) modalTitle.textContent = `${category} Templates`;
            if (modalBack) {
                modalBack.style.display = 'flex';
                modalBack.onclick = () => {
                    // Go back to categories
                    if (categoryStep) categoryStep.style.display = 'block';
                    if (templateStep) templateStep.style.display = 'none';
                    if (modalTitle) modalTitle.textContent = 'Choose Background Category';
                    if (modalBack) modalBack.style.display = 'none';
                };
            }

            // Load templates
            if (templateGrid) {
                templateGrid.innerHTML = '';

                templates.forEach(template => {
                    const templateItem = document.createElement('div');
                    templateItem.className = 'template-item';
                    templateItem.innerHTML = `
                        <img src="${template.url}" alt="${template.name}" class="template-image" loading="lazy">
                        <div class="template-overlay">
                            <span>${template.name}</span>
                        </div>
                    `;

                    templateItem.addEventListener('click', () => {
                        // Remove previous selection
                        templateGrid.querySelectorAll('.template-item').forEach(item => {
                            item.classList.remove('selected');
                        });

                        // Select this template
                        templateItem.classList.add('selected');

                        // Apply background immediately
                        setCanvasBackground(template.url);
                        closeBackgroundModal();
                        console.log('✅ Background applied:', template.name);
                    });

                    templateGrid.appendChild(templateItem);
                });

                console.log('✅ Loaded', templates.length, 'templates for', category);
            }
        }
    }

    function setCanvasBackground(imageUrl) {
        console.log('🔄 Loading background image:', imageUrl);

        fabric.Image.fromURL(imageUrl, (img) => {
            if (img) {
                // Use the original canvas dimensions (800x400), not the scaled dimensions
                const canvasWidth = 800;
                const canvasHeight = 400;

                // Calculate scale to cover entire canvas (like CSS background-size: cover)
                const scaleX = canvasWidth / img.width;
                const scaleY = canvasHeight / img.height;
                const scale = Math.max(scaleX, scaleY);

                console.log('📐 Original Canvas:', canvasWidth, 'x', canvasHeight);
                console.log('📐 Image:', img.width, 'x', img.height);
                console.log('📏 Scale:', scale);

                // Set background image with scale and positioning options
                canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas), {
                    scaleX: scale,
                    scaleY: scale,
                    left: canvasWidth / 2,
                    top: canvasHeight / 2,
                    originX: 'center',
                    originY: 'center'
                });

                console.log('✅ Background image set to cover entire canvas');

            } else {
                console.error('❌ Failed to load background image');
                alert('Failed to load background image. Please try another one.');
            }
        }, { crossOrigin: 'anonymous' });
    }

    // Add Text functionality
    const addTextBtn = document.getElementById('addText');
    if (addTextBtn) {
        addTextBtn.addEventListener('click', () => {
            // Use fabric.IText for interactive text editing
            const text = new fabric.IText('Your Text Here', {
                left: 400,
                top: 200,
                fontFamily: 'Inter',
                fontSize: 24,
                fill: '#000000',
                originX: 'center',
                originY: 'center',
                editable: true,
                selectable: true,
                evented: true,
                // Enable text editing
                isEditing: false,
                editingBorderColor: '#2563eb',
                cursorColor: '#2563eb',
                cursorWidth: 2
            });

            canvas.add(text);
            canvas.setActiveObject(text);
            canvas.renderAll();

            // Show text properties
            const textProperties = document.getElementById('textProperties');
            if (textProperties) {
                textProperties.style.display = 'block';
            }

            // Update button states for new text
            updateButtonStates();

            console.log('✅ Interactive text added - double-click to edit');
        });
    }

    // Clear Background functionality
    const clearBackgroundBtn = document.getElementById('clearBackground');
    if (clearBackgroundBtn) {
        clearBackgroundBtn.addEventListener('click', () => {
            canvas.setBackgroundImage(null, canvas.renderAll.bind(canvas));
            canvas.setBackgroundColor('#ffffff', canvas.renderAll.bind(canvas));
            console.log('✅ Background cleared');
        });
    }

    // Add Image functionality
    const addImageBtn = document.getElementById('addImage');
    const imageUpload = document.getElementById('imageUpload');

    console.log('🔍 Image upload elements:', {
        addImageBtn: !!addImageBtn,
        imageUpload: !!imageUpload
    });

    if (addImageBtn && imageUpload) {
        addImageBtn.addEventListener('click', () => {
            console.log('🔄 Add image button clicked');
            imageUpload.click();
        });

        imageUpload.addEventListener('change', (e) => {
            console.log('🔄 File input changed');
            const file = e.target.files[0];
            if (file) {
                console.log('📁 File selected:', file.name, file.type, file.size);

                // Check file type
                if (!file.type.startsWith('image/')) {
                    alert('Please select an image file');
                    return;
                }

                // Check file size (max 10MB)
                if (file.size > 10 * 1024 * 1024) {
                    alert('Image file is too large. Please select a file smaller than 10MB.');
                    return;
                }

                const reader = new FileReader();
                reader.onload = (event) => {
                    console.log('🔄 File read successfully, creating Fabric image...');
                    fabric.Image.fromURL(event.target.result, (img) => {
                        if (!img) {
                            console.error('❌ Failed to create Fabric image');
                            alert('Failed to load image. Please try a different image.');
                            return;
                        }

                        console.log('✅ Fabric image created:', img.width, 'x', img.height);

                        // Scale image to fit canvas
                        const maxWidth = canvas.width * 0.8;
                        const maxHeight = canvas.height * 0.8;

                        if (img.width > maxWidth || img.height > maxHeight) {
                            const scaleX = maxWidth / img.width;
                            const scaleY = maxHeight / img.height;
                            const scale = Math.min(scaleX, scaleY);

                            img.set({
                                scaleX: scale,
                                scaleY: scale
                            });
                            console.log('📏 Image scaled by factor:', scale);
                        }

                        img.set({
                            left: canvas.width / 2,
                            top: canvas.height / 2,
                            originX: 'center',
                            originY: 'center'
                        });

                        canvas.add(img);
                        canvas.setActiveObject(img);
                        canvas.renderAll();

                        console.log('✅ Image added to canvas successfully');

                        // Clear the file input so same file can be selected again
                        imageUpload.value = '';

                    }, { crossOrigin: 'anonymous' });
                };

                reader.onerror = () => {
                    console.error('❌ Failed to read file');
                    alert('Failed to read the selected file. Please try again.');
                };

                reader.readAsDataURL(file);
            } else {
                console.log('ℹ️ No file selected');
            }
        });
    } else {
        console.error('❌ Image upload elements not found:', {
            addImageBtn: !!addImageBtn,
            imageUpload: !!imageUpload
        });
    }

    // Clear All functionality
    const clearAllBtn = document.getElementById('clearAll');
    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', () => {
            canvas.clear();
            canvas.setBackgroundColor('#ffffff', canvas.renderAll.bind(canvas));
            console.log('✅ Canvas cleared');
        });
    }

    // Super HD Export functionality
    const downloadBtn = document.getElementById('downloadImage');
    const exportQuality = document.getElementById('exportQuality');
    const exportFormat = document.getElementById('exportFormat');

    if (downloadBtn) {
        downloadBtn.addEventListener('click', async () => {
            try {
                await exportSuperHD();
            } catch (error) {
                console.error('❌ Export failed:', error);
                hideExportProgress();
                alert('Export failed: ' + error.message);
            }
        });
    }

    // Checkout Button functionality
    const checkoutBtn = document.getElementById('checkoutBtn');
    if (checkoutBtn) {
        checkoutBtn.addEventListener('click', async () => {
            try {
                console.log('🛒 Starting checkout process...');

                // Show loading state
                checkoutBtn.disabled = true;
                checkoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Preparing...</span>';

                // Export canvas as high-quality image for checkout
                const dataURL = canvas.toDataURL({
                    format: 'png',
                    quality: 1.0,
                    multiplier: 2, // High quality for checkout
                    enableRetinaScaling: true
                });

                // Prepare order data for checkout system
                const orderData = {
                    imageData: dataURL,
                    billboardType: 'custom',
                    customerEmail: '', // Will be filled in checkout modal
                    customerName: '', // Will be filled in checkout modal
                    designData: {
                        canvasObjects: canvas.toJSON(),
                        canvasWidth: canvas.width,
                        canvasHeight: canvas.height,
                        timestamp: new Date().toISOString()
                    }
                };

                // Save order data to session storage for checkout modal
                sessionStorage.setItem('billboardOrderData', JSON.stringify(orderData));

                // Initialize and open checkout modal
                if (typeof window.unifiedCheckout === 'undefined') {
                    window.unifiedCheckout = new UnifiedCheckout();
                }

                await window.unifiedCheckout.open();

                console.log('✅ Checkout modal opened successfully');

            } catch (error) {
                console.error('❌ Checkout failed:', error);
                alert('Failed to start checkout process. Please try again.');
            } finally {
                // Reset button state
                checkoutBtn.disabled = false;
                checkoutBtn.innerHTML = '<i class="fas fa-shopping-cart"></i> <span>Checkout</span>';
            }
        });
    }

    // Super HD Export Function
    async function exportSuperHD() {
        const quality = exportQuality ? exportQuality.value : 'standard';
        const format = exportFormat ? exportFormat.value : 'png';

        // Show progress indicator
        showExportProgress('Preparing super HD export...');

        // Configure quality settings based on research
        let multiplier, jpegQuality, expectedSize;
        switch (quality) {
            case 'super':
                multiplier = 6;
                jpegQuality = 0.98;
                expectedSize = '~13MB';
                break;
            case 'ultra':
                multiplier = 4;
                jpegQuality = 0.95;
                expectedSize = '~8MB';
                break;
            case 'high':
                multiplier = 3;
                jpegQuality = 0.92;
                expectedSize = '~5MB';
                break;
            case 'standard':
                multiplier = 2;
                jpegQuality = 0.9;
                expectedSize = '~2MB';
                break;
            default: // web
                multiplier = 1;
                jpegQuality = 0.8;
                expectedSize = '~500KB';
        }

        console.log(`🚀 Starting ${quality} quality export (${multiplier}x multiplier)`);

        showExportProgress(`Generating ${quality} quality image (${expectedSize})...`);

        // Configure export options for maximum quality
        const exportOptions = {
            format: format,
            quality: jpegQuality,
            multiplier: multiplier,
            enableRetinaScaling: true,
            withoutTransform: false,
            withoutShadow: false
        };

        // Export with high quality
        const dataURL = canvas.toDataURL(exportOptions);

        showExportProgress('Preparing download...');

        // Calculate actual file size
        const base64Length = dataURL.length - (dataURL.indexOf(',') + 1);
        const sizeInBytes = (base64Length * 3) / 4;
        const sizeInMB = (sizeInBytes / (1024 * 1024)).toFixed(2);

        // Create download with descriptive filename
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const qualityLabel = quality.charAt(0).toUpperCase() + quality.slice(1);
        const link = document.createElement('a');
        link.download = `billboard-${qualityLabel}-${multiplier}x-${timestamp}.${format}`;
        link.href = dataURL;

        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        hideExportProgress();

        // Ensure progress is hidden even if there are timing issues
        setTimeout(() => {
            hideExportProgress();
        }, 500);

        showExportSuccess(`${format.toUpperCase()} exported successfully!`, `${qualityLabel} quality (${multiplier}x) - ${sizeInMB}MB`);

        console.log(`✅ Super HD export complete: ${sizeInMB}MB file generated`);
    }

    // Export Progress and Feedback Functions
    function showExportProgress(message) {
        hideExportProgress();

        const progressDiv = document.createElement('div');
        progressDiv.id = 'export-progress';
        progressDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            padding: 16px 20px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(37, 99, 235, 0.3);
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            font-size: 14px;
            font-weight: 500;
            max-width: 300px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            animation: slideInRight 0.3s ease-out;
        `;

        progressDiv.innerHTML = `
            <div style="display: flex; align-items: center; gap: 12px;">
                <div style="width: 20px; height: 20px; border: 2px solid rgba(255,255,255,0.3); border-top: 2px solid white; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                <div>
                    <div style="font-weight: 600; margin-bottom: 2px;">Exporting...</div>
                    <div style="font-size: 12px; opacity: 0.9;">${message}</div>
                </div>
            </div>
        `;

        // Add animations if not already added
        if (!document.getElementById('export-animations')) {
            const style = document.createElement('style');
            style.id = 'export-animations';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(progressDiv);
    }

    function hideExportProgress() {
        const progressDiv = document.getElementById('export-progress');
        if (progressDiv) {
            progressDiv.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (progressDiv && progressDiv.parentNode) {
                    progressDiv.remove();
                }
            }, 300);
        }

        // Fallback: Force remove any stuck progress indicators after 1 second
        setTimeout(() => {
            const stuckProgress = document.getElementById('export-progress');
            if (stuckProgress) {
                stuckProgress.remove();
                console.log('🧹 Removed stuck export progress indicator');
            }
        }, 1000);
    }

    function showExportSuccess(title, details) {
        const successDiv = document.createElement('div');
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #059669, #047857);
            color: white;
            padding: 16px 20px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(5, 150, 105, 0.3);
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            font-size: 14px;
            max-width: 320px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            animation: slideInRight 0.3s ease-out;
        `;

        successDiv.innerHTML = `
            <div style="display: flex; align-items: center; gap: 12px;">
                <span style="font-size: 24px;">✅</span>
                <div>
                    <div style="font-weight: 600; margin-bottom: 2px;">${title}</div>
                    <div style="font-size: 12px; opacity: 0.9;">${details}</div>
                </div>
            </div>
        `;

        document.body.appendChild(successDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            successDiv.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => successDiv.remove(), 300);
        }, 5000);
    }

    // Load Google Fonts
    function loadGoogleFonts() {
        const fonts = [
            'Inter:wght@400;500;600;700',
            'Roboto:wght@400;500;700',
            'Open+Sans:wght@400;600;700',
            'Montserrat:wght@400;500;600;700',
            'Lato:wght@400;700',
            'Poppins:wght@400;500;600;700',
            'Playfair+Display:wght@400;700',
            'Merriweather:wght@400;700',
            'Oswald:wght@400;500;600',
            'Raleway:wght@400;500;600;700',
            'Nunito:wght@400;600;700',
            'Source+Sans+Pro:wght@400;600;700',
            'Ubuntu:wght@400;500;700',
            'Crimson+Text:wght@400;600',
            'Libre+Baskerville:wght@400;700'
        ];

        const link = document.createElement('link');
        link.href = `https://fonts.googleapis.com/css2?${fonts.map(f => `family=${f}`).join('&')}&display=swap`;
        link.rel = 'stylesheet';
        document.head.appendChild(link);

        console.log('✅ Google Fonts loaded');
    }

    loadGoogleFonts();

    // Enhanced font family dropdown with previews
    function setupFontDropdown() {
        const fontFamily = document.getElementById('fontFamily');
        if (fontFamily) {
            const fonts = [
                { name: 'Inter', family: 'Inter, sans-serif' },
                { name: 'Roboto', family: 'Roboto, sans-serif' },
                { name: 'Open Sans', family: 'Open Sans, sans-serif' },
                { name: 'Montserrat', family: 'Montserrat, sans-serif' },
                { name: 'Lato', family: 'Lato, sans-serif' },
                { name: 'Poppins', family: 'Poppins, sans-serif' },
                { name: 'Playfair Display', family: 'Playfair Display, serif' },
                { name: 'Merriweather', family: 'Merriweather, serif' },
                { name: 'Oswald', family: 'Oswald, sans-serif' },
                { name: 'Raleway', family: 'Raleway, sans-serif' },
                { name: 'Nunito', family: 'Nunito, sans-serif' },
                { name: 'Source Sans Pro', family: 'Source Sans Pro, sans-serif' },
                { name: 'Ubuntu', family: 'Ubuntu, sans-serif' },
                { name: 'Crimson Text', family: 'Crimson Text, serif' },
                { name: 'Libre Baskerville', family: 'Libre Baskerville, serif' },
                { name: 'Arial', family: 'Arial, sans-serif' },
                { name: 'Helvetica', family: 'Helvetica, sans-serif' },
                { name: 'Times New Roman', family: 'Times New Roman, serif' },
                { name: 'Georgia', family: 'Georgia, serif' },
                { name: 'Verdana', family: 'Verdana, sans-serif' }
            ];

            fontFamily.innerHTML = '';
            fonts.forEach(font => {
                const option = document.createElement('option');
                option.value = font.name;
                option.textContent = font.name;
                option.style.fontFamily = font.family;
                fontFamily.appendChild(option);
            });
        }
    }

    setupFontDropdown();

    // Text property controls
    const fontSize = document.getElementById('fontSize');
    const fontColor = document.getElementById('fontColor');
    const fontFamily = document.getElementById('fontFamily');

    function updateSelectedText(property, value) {
        const activeObjects = canvas.getActiveObjects();
        const textObjects = activeObjects.filter(obj => obj.type === 'text' || obj.type === 'i-text');

        if (textObjects.length > 0) {
            textObjects.forEach(obj => {
                obj.set(property, value);
            });
            canvas.renderAll();
            console.log('✅ Text property updated:', property, '=', value);
        }
    }

    if (fontSize) {
        fontSize.addEventListener('input', (e) => {
            const value = parseInt(e.target.value);
            if (value >= 8 && value <= 200) {
                updateSelectedText('fontSize', value);
            }
        });

        fontSize.addEventListener('change', (e) => {
            const value = parseInt(e.target.value);
            if (value < 8) e.target.value = 8;
            if (value > 200) e.target.value = 200;
        });
    }

    if (fontColor) {
        fontColor.addEventListener('change', (e) => {
            updateSelectedText('fill', e.target.value);
        });
    }

    if (fontFamily) {
        fontFamily.addEventListener('change', (e) => {
            updateSelectedText('fontFamily', e.target.value);
        });
    }

    // Text Style Controls (Bold, Italic)
    const boldToggle = document.getElementById('boldToggle');
    const italicToggle = document.getElementById('italicToggle');

    if (boldToggle) {
        boldToggle.addEventListener('click', () => {
            const activeObjects = canvas.getActiveObjects();
            const textObjects = activeObjects.filter(obj => obj.type === 'text' || obj.type === 'i-text');

            if (textObjects.length > 0) {
                textObjects.forEach(obj => {
                    const currentWeight = obj.fontWeight || 'normal';
                    const newWeight = currentWeight === 'bold' ? 'normal' : 'bold';
                    obj.set('fontWeight', newWeight);
                });
                canvas.renderAll();
                updateButtonStates();
                console.log('✅ Bold toggled');
            }
        });
    }

    if (italicToggle) {
        italicToggle.addEventListener('click', () => {
            const activeObjects = canvas.getActiveObjects();
            const textObjects = activeObjects.filter(obj => obj.type === 'text' || obj.type === 'i-text');

            if (textObjects.length > 0) {
                textObjects.forEach(obj => {
                    const currentStyle = obj.fontStyle || 'normal';
                    const newStyle = currentStyle === 'italic' ? 'normal' : 'italic';
                    obj.set('fontStyle', newStyle);
                });
                canvas.renderAll();
                updateButtonStates();
                console.log('✅ Italic toggled');
            }
        });
    }

    // Text Alignment Controls
    const alignLeft = document.getElementById('alignLeft');
    const alignCenter = document.getElementById('alignCenter');
    const alignRight = document.getElementById('alignRight');
    const alignJustify = document.getElementById('alignJustify');

    function setTextAlignment(alignment) {
        const activeObjects = canvas.getActiveObjects();
        const textObjects = activeObjects.filter(obj => obj.type === 'text' || obj.type === 'i-text');

        if (textObjects.length > 0) {
            textObjects.forEach(obj => {
                obj.set('textAlign', alignment);
            });
            canvas.renderAll();
            updateButtonStates();
            console.log('✅ Text alignment set to:', alignment);
        }
    }

    if (alignLeft) {
        alignLeft.addEventListener('click', () => setTextAlignment('left'));
    }
    if (alignCenter) {
        alignCenter.addEventListener('click', () => setTextAlignment('center'));
    }
    if (alignRight) {
        alignRight.addEventListener('click', () => setTextAlignment('right'));
    }
    if (alignJustify) {
        alignJustify.addEventListener('click', () => setTextAlignment('justify'));
    }

    // Text Shadow Controls
    const shadowToggle = document.getElementById('shadowToggle');
    const shadowControls = document.getElementById('shadowControls');
    const shadowType = document.getElementById('shadowType');
    const offsetControls = document.getElementById('offsetControls');
    const shadowColor = document.getElementById('shadowColor');
    const shadowBlur = document.getElementById('shadowBlur');
    const shadowOffsetX = document.getElementById('shadowOffsetX');
    const shadowOffsetY = document.getElementById('shadowOffsetY');
    const shadowOpacity = document.getElementById('shadowOpacity');

    // Shadow value display elements
    const shadowBlurValue = document.getElementById('shadowBlurValue');
    const shadowOffsetXValue = document.getElementById('shadowOffsetXValue');
    const shadowOffsetYValue = document.getElementById('shadowOffsetYValue');
    const shadowOpacityValue = document.getElementById('shadowOpacityValue');

    let shadowEnabled = false;

    function updateShadowValues() {
        if (shadowBlurValue) shadowBlurValue.textContent = shadowBlur.value;
        if (shadowOffsetXValue) shadowOffsetXValue.textContent = shadowOffsetX.value;
        if (shadowOffsetYValue) shadowOffsetYValue.textContent = shadowOffsetY.value;
        if (shadowOpacityValue) shadowOpacityValue.textContent = shadowOpacity.value + '%';
    }

    function applyShadowToText() {
        const activeObjects = canvas.getActiveObjects();
        const textObjects = activeObjects.filter(obj => obj.type === 'text' || obj.type === 'i-text');

        if (textObjects.length > 0) {
            textObjects.forEach(obj => {
                if (shadowEnabled) {
                    const shadowColorValue = shadowColor.value;
                    const blur = parseInt(shadowBlur.value);
                    const opacity = parseInt(shadowOpacity.value) / 100;
                    const shadowTypeValue = shadowType.value;

                    // Determine offset based on shadow type
                    let offsetX, offsetY;
                    if (shadowTypeValue === 'glow') {
                        // Glow effect: no offset, visible through blur
                        offsetX = 0;
                        offsetY = 0;
                    } else {
                        // Drop shadow: use offset values
                        offsetX = parseInt(shadowOffsetX.value);
                        offsetY = parseInt(shadowOffsetY.value);
                    }

                    // Convert hex color to rgba with opacity
                    const r = parseInt(shadowColorValue.substr(1, 2), 16);
                    const g = parseInt(shadowColorValue.substr(3, 2), 16);
                    const b = parseInt(shadowColorValue.substr(5, 2), 16);
                    const shadowColorRgba = `rgba(${r}, ${g}, ${b}, ${opacity})`;

                    obj.set('shadow', {
                        color: shadowColorRgba,
                        blur: blur,
                        offsetX: offsetX,
                        offsetY: offsetY
                    });

                    console.log('✅ Text shadow applied:', shadowTypeValue, `blur:${blur}`, `offset:${offsetX},${offsetY}`);
                } else {
                    obj.set('shadow', null);
                    console.log('✅ Text shadow removed');
                }
            });
            canvas.renderAll();
        }
    }

    if (shadowToggle) {
        shadowToggle.addEventListener('click', () => {
            shadowEnabled = !shadowEnabled;
            shadowToggle.classList.toggle('active', shadowEnabled);

            if (shadowControls) {
                shadowControls.style.display = shadowEnabled ? 'block' : 'none';
            }

            applyShadowToText();
            updateButtonStates();
        });
    }

    // Shadow type change handler
    if (shadowType) {
        shadowType.addEventListener('change', () => {
            const isDropShadow = shadowType.value === 'drop';
            if (offsetControls) {
                offsetControls.style.display = isDropShadow ? 'block' : 'none';
            }

            // Apply better defaults based on type
            if (shadowType.value === 'glow') {
                // Glow: higher blur, no offset
                if (shadowBlur) shadowBlur.value = 8;
                console.log('✅ Switched to glow mode - blur increased for visibility');
            } else {
                // Drop shadow: moderate blur, visible offset
                if (shadowBlur) shadowBlur.value = 3;
                if (shadowOffsetX) shadowOffsetX.value = 2;
                if (shadowOffsetY) shadowOffsetY.value = 2;
                console.log('✅ Switched to drop shadow mode - offset enabled');
            }

            updateShadowValues();
            applyShadowToText();
        });
    }

    // Shadow control event listeners
    if (shadowColor) {
        shadowColor.addEventListener('change', applyShadowToText);
    }

    if (shadowBlur) {
        shadowBlur.addEventListener('input', () => {
            updateShadowValues();
            applyShadowToText();
        });
    }

    if (shadowOffsetX) {
        shadowOffsetX.addEventListener('input', () => {
            updateShadowValues();
            applyShadowToText();
        });
    }

    if (shadowOffsetY) {
        shadowOffsetY.addEventListener('input', () => {
            updateShadowValues();
            applyShadowToText();
        });
    }

    if (shadowOpacity) {
        shadowOpacity.addEventListener('input', () => {
            updateShadowValues();
            applyShadowToText();
        });
    }

    // Initialize shadow value displays
    updateShadowValues();

    // Function to update button states based on selected text
    function updateButtonStates() {
        const activeObjects = canvas.getActiveObjects();
        const textObjects = activeObjects.filter(obj => obj.type === 'text' || obj.type === 'i-text');

        if (textObjects.length > 0) {
            const firstTextObj = textObjects[0];

            // Update bold button state
            if (boldToggle) {
                const isBold = firstTextObj.fontWeight === 'bold';
                boldToggle.classList.toggle('active', isBold);
            }

            // Update italic button state
            if (italicToggle) {
                const isItalic = firstTextObj.fontStyle === 'italic';
                italicToggle.classList.toggle('active', isItalic);
            }

            // Update alignment button states
            const textAlign = firstTextObj.textAlign || 'left';
            if (alignLeft) alignLeft.classList.toggle('active', textAlign === 'left');
            if (alignCenter) alignCenter.classList.toggle('active', textAlign === 'center');
            if (alignRight) alignRight.classList.toggle('active', textAlign === 'right');
            if (alignJustify) alignJustify.classList.toggle('active', textAlign === 'justify');

            // Update shadow button state
            if (shadowToggle) {
                const hasShadow = firstTextObj.shadow !== null && firstTextObj.shadow !== undefined;
                shadowToggle.classList.toggle('active', hasShadow);
                shadowEnabled = hasShadow;

                if (shadowControls) {
                    shadowControls.style.display = hasShadow ? 'block' : 'none';
                }

                // Update shadow control values if shadow exists
                if (hasShadow && firstTextObj.shadow) {
                    const shadow = firstTextObj.shadow;
                    if (shadowBlur) shadowBlur.value = shadow.blur || 5;
                    if (shadowOffsetX) shadowOffsetX.value = shadow.offsetX || 0;
                    if (shadowOffsetY) shadowOffsetY.value = shadow.offsetY || 0;

                    // Determine shadow type based on offset
                    const isGlow = (shadow.offsetX === 0 && shadow.offsetY === 0);
                    if (shadowType) {
                        shadowType.value = isGlow ? 'glow' : 'drop';
                        // Show/hide offset controls based on type
                        if (offsetControls) {
                            offsetControls.style.display = isGlow ? 'none' : 'block';
                        }
                    }

                    // Extract opacity from rgba color if possible
                    if (shadow.color && shadow.color.includes('rgba')) {
                        const opacityMatch = shadow.color.match(/rgba\([^,]+,[^,]+,[^,]+,([^)]+)\)/);
                        if (opacityMatch && shadowOpacity) {
                            shadowOpacity.value = Math.round(parseFloat(opacityMatch[1]) * 100);
                        }
                    }

                    updateShadowValues();
                }
            }
        } else {
            // Clear all button states when no text is selected
            if (boldToggle) boldToggle.classList.remove('active');
            if (italicToggle) italicToggle.classList.remove('active');
            if (alignLeft) alignLeft.classList.remove('active');
            if (alignCenter) alignCenter.classList.remove('active');
            if (alignRight) alignRight.classList.remove('active');
            if (alignJustify) alignJustify.classList.remove('active');
            if (shadowToggle) {
                shadowToggle.classList.remove('active');
                shadowEnabled = false;
                if (shadowControls) shadowControls.style.display = 'none';
            }
        }
    }

    // Helper function to update text properties panel
    function updateTextPropertiesPanel(textObj) {
        const textProperties = document.getElementById('textProperties');
        if (textProperties && textObj && (textObj.type === 'text' || textObj.type === 'i-text')) {
            textProperties.style.display = 'block';

            // Update controls with current values
            if (fontSize) fontSize.value = textObj.fontSize || 24;
            if (fontColor) fontColor.value = textObj.fill || '#000000';
            if (fontFamily) fontFamily.value = textObj.fontFamily || 'Inter';

            // Update button states
            updateButtonStates();
        }
    }

    // Selection handling
    canvas.on('selection:created', (e) => {
        const obj = e.selected ? e.selected[0] : e.target;
        if (obj && (obj.type === 'text' || obj.type === 'i-text')) {
            updateTextPropertiesPanel(obj);
        }

        // Show delete instructions
        showDeleteInstructions();
    });

    canvas.on('selection:updated', (e) => {
        const obj = e.selected ? e.selected[0] : e.target;
        if (obj && (obj.type === 'text' || obj.type === 'i-text')) {
            updateTextPropertiesPanel(obj);
        }

        // Show delete instructions
        showDeleteInstructions();
    });

    canvas.on('selection:cleared', () => {
        const textProperties = document.getElementById('textProperties');
        if (textProperties) {
            textProperties.style.display = 'none';
        }
        // Clear button states when no text is selected
        updateButtonStates();
        hideDeleteInstructions();
    });

    // Helper functions for delete instructions
    function showDeleteInstructions() {
        // Create or show delete instructions
        let instructions = document.getElementById('deleteInstructions');
        if (!instructions) {
            instructions = document.createElement('div');
            instructions.id = 'deleteInstructions';
            instructions.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 10px 15px;
                border-radius: 6px;
                font-size: 14px;
                z-index: 1000;
                backdrop-filter: blur(4px);
            `;
            instructions.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-info-circle"></i>
                    <span>Press <kbd style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 3px;">Delete</kbd> or right-click to remove</span>
                </div>
            `;
            document.body.appendChild(instructions);
        }
        instructions.style.display = 'block';

        // Auto-hide after 3 seconds
        setTimeout(() => {
            hideDeleteInstructions();
        }, 3000);
    }

    function hideDeleteInstructions() {
        const instructions = document.getElementById('deleteInstructions');
        if (instructions) {
            instructions.style.display = 'none';
        }
    }

    console.log('✅ Basic toolbar initialized');
}

// Responsive Layout Management
function setupResponsiveLayout() {
    function handleLayoutChange() {
        const isDesktop = window.innerWidth >= 768; // 48rem = 768px

        if (isDesktop) {
            moveToDesktopLayout();
        } else {
            moveToMobileLayout();
        }
    }

    function moveToDesktopLayout() {
        console.log('📱➡️💻 Switching to desktop layout');

        // Move design tools to left sidebar
        const designTools = document.querySelectorAll('#backgroundSection, #textSection');
        const leftSidebar = document.getElementById('leftSidebarContent');

        designTools.forEach(section => {
            if (section && leftSidebar) {
                leftSidebar.appendChild(section);
            }
        });

        // Move export tools to right sidebar
        const exportSection = document.getElementById('exportSection');
        const rightSidebar = document.getElementById('rightSidebarContent');

        if (exportSection && rightSidebar) {
            rightSidebar.appendChild(exportSection);
        }

        console.log('✅ Desktop layout activated');
    }

    function moveToMobileLayout() {
        console.log('💻➡️📱 Switching to mobile layout');

        // Move all sections back to mobile toolbar
        const allSections = document.querySelectorAll('#backgroundSection, #textSection, #exportSection');
        const mobileToolbarContent = document.getElementById('toolbarContent');

        allSections.forEach(section => {
            if (section && mobileToolbarContent) {
                mobileToolbarContent.appendChild(section);
            }
        });

        console.log('✅ Mobile layout activated');
    }

    // Initial layout setup
    handleLayoutChange();

    // Listen for window resize with debouncing
    let resizeTimeout;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(handleLayoutChange, 150);
    });

    // Listen for orientation change on mobile devices
    window.addEventListener('orientationchange', () => {
        setTimeout(handleLayoutChange, 200);
    });

    // Setup sticky sidebar enhancements for desktop
    if (window.innerWidth >= 768) {
        setupStickySidebarEnhancements();
        preventHeaderOverlap();
    }
}

// Sticky Sidebar Enhancements
function setupStickySidebarEnhancements() {
    const leftSidebar = document.getElementById('leftSidebarContent');
    const rightSidebar = document.getElementById('rightSidebarContent');

    function addScrollEnhancements(sidebar) {
        if (!sidebar) return;

        // Add scroll event listener for visual feedback
        sidebar.addEventListener('scroll', function() {
            const scrollTop = this.scrollTop;
            const sectionHeaders = this.querySelectorAll('.section-header');

            sectionHeaders.forEach(header => {
                if (scrollTop > 10) {
                    header.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
                } else {
                    header.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.05)';
                }
            });
        });

        // Smooth scroll to sections
        const sectionHeaders = sidebar.querySelectorAll('.section-header');
        sectionHeaders.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                const sectionContent = this.nextElementSibling;
                if (sectionContent) {
                    sectionContent.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest'
                    });
                }
            });
        });
    }

    addScrollEnhancements(leftSidebar);
    addScrollEnhancements(rightSidebar);

    console.log('✅ Sticky sidebar enhancements activated');
}

// Prevent Header Overlap Function
function preventHeaderOverlap() {
    const sidebars = document.querySelectorAll('.desktop-left-sidebar, .desktop-right-sidebar');
    const sidebarHeaders = document.querySelectorAll('.sidebar-header');
    const sidebarContent = document.querySelectorAll('.sidebar-content');

    // Ensure sidebars don't extend into header space
    sidebars.forEach(sidebar => {
        sidebar.style.top = '0';
        sidebar.style.marginTop = '0';
        sidebar.style.maxHeight = 'calc(100vh - 80px)'; // Account for fixed header
    });

    // Ensure sidebar headers stick properly without overlapping main header
    sidebarHeaders.forEach(header => {
        header.style.position = 'sticky';
        header.style.top = '0';
        header.style.zIndex = '10'; // Lower than main header (1000)
        header.style.backgroundColor = 'rgba(241, 245, 249, 0.95)';
        header.style.backdropFilter = 'blur(8px)';
    });

    // Ensure sidebar content has proper sticky positioning
    sidebarContent.forEach(content => {
        content.style.position = 'sticky';
        content.style.top = '0';
        content.style.maxHeight = 'calc(100vh - 80px - 60px)'; // Header + sidebar header
        content.style.overflowY = 'auto';
    });

    // Add scroll event to manage visual feedback
    sidebarContent.forEach(content => {
        content.addEventListener('scroll', function() {
            const scrollTop = this.scrollTop;
            const headers = this.querySelectorAll('.section-header');

            headers.forEach(header => {
                if (scrollTop > 5) {
                    header.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
                } else {
                    header.style.boxShadow = 'none';
                }
            });
        });
    });

    console.log('✅ Header overlap prevention applied');
}

// Initialize Checkout System
function initializeCheckoutSystem() {
    // Initialize the unified checkout system
    if (typeof UnifiedCheckout !== 'undefined') {
        window.unifiedCheckout = new UnifiedCheckout();
        console.log('✅ Checkout system initialized');
    } else {
        console.warn('⚠️ UnifiedCheckout class not found - checkout functionality may not work');
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(initializeSimpleCanvas, 500);
    });
} else {
    setTimeout(initializeSimpleCanvas, 500);
}
</script>

<!-- Background Template Data -->
<script src="background-template-data.js"></script>

<!-- Include Checkout Modal -->
<?php include '../shared/checkout-modal.php'; ?>

<!-- Checkout Modal System -->
<script src="../shared/checkout-modal.js"></script>
<script src="../shared/order-data-manager.js"></script>

</body>
</html>
