/* ========================================
   MOBILE EXPORT MANAGER
   ======================================== */

/**
 * MobileExportManager - Handles image export and checkout integration
 * Optimized for mobile devices with quality settings and responsive UI
 */
class MobileExportManager {
    constructor(canvasManager) {
        this.canvasManager = canvasManager;
        this.canvas = canvasManager.canvas;
        
        // Export settings
        this.exportSettings = {
            quality: 'standard',
            format: 'png',
            multiplier: 2,
            backgroundColor: '#ffffff'
        };
        
        // Quality presets
        this.qualityPresets = {
            web: { multiplier: 1, dpi: 72 },
            standard: { multiplier: 2, dpi: 150 },
            high: { multiplier: 4, dpi: 300 }
        };
        
        // Export state
        this.isExporting = false;
        this.exportProgress = 0;
        
        // Mobile optimization
        this.isMobile = this.detectMobile();
        this.maxMobileSize = 2048; // Max dimension for mobile export
        
        this.init();
    }
    
    /**
     * Initialize export manager
     */
    init() {
        this.setupEventListeners();
        this.setupExportControls();
        
        console.log('MobileExportManager initialized');
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for toolbar export events
        document.addEventListener('toolbar:export:requested', () => {
            this.exportImage();
        });
        
        document.addEventListener('toolbar:checkout:requested', () => {
            this.initiateCheckout();
        });
        
        // Export quality change
        const exportQuality = document.getElementById('exportQuality');
        if (exportQuality) {
            exportQuality.addEventListener('change', (e) => {
                this.setExportQuality(e.target.value);
            });
        }
        
        // Export format change
        const exportFormat = document.getElementById('exportFormat');
        if (exportFormat) {
            exportFormat.addEventListener('change', (e) => {
                this.setExportFormat(e.target.value);
            });
        }
    }
    
    /**
     * Setup export controls
     */
    setupExportControls() {
        // Initialize default settings
        this.updateExportControls();
    }
    
    /**
     * Set export quality
     */
    setExportQuality(quality) {
        if (this.qualityPresets[quality]) {
            this.exportSettings.quality = quality;
            this.exportSettings.multiplier = this.qualityPresets[quality].multiplier;
            
            // Adjust for mobile limitations
            if (this.isMobile && this.exportSettings.multiplier > 2) {
                this.exportSettings.multiplier = 2;
                this.showToast('Export quality adjusted for mobile device', 'warning');
            }
            
            this.emit('quality:changed', { quality, settings: this.exportSettings });
        }
    }
    
    /**
     * Set export format
     */
    setExportFormat(format) {
        this.exportSettings.format = format;
        this.emit('format:changed', { format });
    }
    
    /**
     * Export canvas as image
     */
    async exportImage() {
        if (this.isExporting) {
            this.showToast('Export already in progress', 'warning');
            return;
        }
        
        try {
            this.isExporting = true;
            this.showExportProgress();
            
            // Prepare canvas for export
            this.prepareCanvasForExport();
            
            // Generate image data
            const imageData = await this.generateImageData();
            
            // Download image
            this.downloadImage(imageData);
            
            this.showToast('Image exported successfully');
            this.emit('export:success', { settings: this.exportSettings });
            
        } catch (error) {
            console.error('Export failed:', error);
            this.showToast('Export failed. Please try again.', 'error');
            this.emit('export:error', { error });
            
        } finally {
            this.isExporting = false;
            this.hideExportProgress();
            this.restoreCanvasAfterExport();
        }
    }
    
    /**
     * Prepare canvas for export
     */
    prepareCanvasForExport() {
        // Store current state
        this.originalViewport = this.canvas.viewportTransform.slice();
        this.originalZoom = this.canvas.getZoom();
        
        // Reset viewport for clean export
        this.canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
        this.canvas.setZoom(1);
        
        // Ensure all objects are within canvas bounds
        this.ensureObjectsInBounds();
        
        // Add export class for styling
        this.canvas.upperCanvasEl.classList.add('export-mode');
        
        this.canvas.renderAll();
    }
    
    /**
     * Restore canvas after export
     */
    restoreCanvasAfterExport() {
        // Restore viewport
        this.canvas.setViewportTransform(this.originalViewport);
        this.canvas.setZoom(this.originalZoom);
        
        // Remove export class
        this.canvas.upperCanvasEl.classList.remove('export-mode');
        
        this.canvas.renderAll();
    }
    
    /**
     * Ensure all objects are within canvas bounds
     */
    ensureObjectsInBounds() {
        const objects = this.canvas.getObjects();
        
        objects.forEach(obj => {
            if (obj.isBackground) return;
            
            const bounds = obj.getBoundingRect();
            
            // Check if object is outside canvas
            if (bounds.left < 0 || bounds.top < 0 || 
                bounds.left + bounds.width > this.canvas.width ||
                bounds.top + bounds.height > this.canvas.height) {
                
                // Move object to fit within canvas
                const newLeft = Math.max(0, Math.min(bounds.left, this.canvas.width - bounds.width));
                const newTop = Math.max(0, Math.min(bounds.top, this.canvas.height - bounds.height));
                
                obj.set({
                    left: newLeft + (obj.left - bounds.left),
                    top: newTop + (obj.top - bounds.top)
                });
            }
        });
    }
    
    /**
     * Generate image data
     */
    async generateImageData() {
        return new Promise((resolve, reject) => {
            try {
                const options = {
                    format: this.exportSettings.format,
                    quality: this.exportSettings.format === 'jpeg' ? 0.9 : 1,
                    multiplier: this.exportSettings.multiplier,
                    left: 0,
                    top: 0,
                    width: this.canvas.width,
                    height: this.canvas.height
                };
                
                // Check for mobile size limitations
                const finalWidth = this.canvas.width * options.multiplier;
                const finalHeight = this.canvas.height * options.multiplier;
                
                if (this.isMobile && (finalWidth > this.maxMobileSize || finalHeight > this.maxMobileSize)) {
                    const scale = this.maxMobileSize / Math.max(finalWidth, finalHeight);
                    options.multiplier *= scale;
                    this.showToast('Export size adjusted for mobile device', 'info');
                }
                
                const dataURL = this.canvas.toDataURL(options);
                resolve(dataURL);
                
            } catch (error) {
                reject(error);
            }
        });
    }
    
    /**
     * Download image
     */
    downloadImage(dataURL) {
        const link = document.createElement('a');
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `billboard-design-${timestamp}.${this.exportSettings.format}`;
        
        link.download = filename;
        link.href = dataURL;
        
        // For mobile devices, open in new tab if download doesn't work
        if (this.isMobile) {
            link.target = '_blank';
        }
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    
    /**
     * Initiate checkout process
     */
    async initiateCheckout() {
        try {
            // Validate canvas has content
            if (!this.validateCanvasForCheckout()) {
                this.showToast('Please add some content before checkout', 'warning');
                return;
            }
            
            this.showCheckoutProgress();
            
            // Generate preview image for checkout
            const previewImage = await this.generatePreviewImage();
            
            // Prepare checkout data
            const checkoutData = {
                canvasData: this.canvasManager.exportAsJSON(),
                previewImage: previewImage,
                settings: this.exportSettings,
                timestamp: Date.now()
            };
            
            // Save to session storage for checkout process
            sessionStorage.setItem('billboardDesign', JSON.stringify(checkoutData));
            
            // Open checkout modal (assuming it exists from shared components)
            if (typeof openUnifiedCheckout === 'function') {
                openUnifiedCheckout();
            } else {
                this.showToast('Checkout system not available', 'error');
            }
            
            this.emit('checkout:initiated', { data: checkoutData });
            
        } catch (error) {
            console.error('Checkout initiation failed:', error);
            this.showToast('Failed to initiate checkout', 'error');
            this.emit('checkout:error', { error });
            
        } finally {
            this.hideCheckoutProgress();
        }
    }
    
    /**
     * Validate canvas for checkout
     */
    validateCanvasForCheckout() {
        const objects = this.canvas.getObjects().filter(obj => !obj.isBackground);
        return objects.length > 0;
    }
    
    /**
     * Generate preview image for checkout
     */
    async generatePreviewImage() {
        const originalSettings = { ...this.exportSettings };
        
        // Use lower quality for preview
        this.setExportQuality('web');
        this.setExportFormat('jpeg');
        
        try {
            this.prepareCanvasForExport();
            const previewData = await this.generateImageData();
            this.restoreCanvasAfterExport();
            
            // Restore original settings
            this.exportSettings = originalSettings;
            this.updateExportControls();
            
            return previewData;
            
        } catch (error) {
            this.restoreCanvasAfterExport();
            this.exportSettings = originalSettings;
            throw error;
        }
    }
    
    /**
     * Show export progress
     */
    showExportProgress() {
        this.showProgressOverlay('Exporting image...', 'fas fa-download');
    }
    
    /**
     * Hide export progress
     */
    hideExportProgress() {
        this.hideProgressOverlay();
    }
    
    /**
     * Show checkout progress
     */
    showCheckoutProgress() {
        this.showProgressOverlay('Preparing checkout...', 'fas fa-shopping-cart');
    }
    
    /**
     * Hide checkout progress
     */
    hideCheckoutProgress() {
        this.hideProgressOverlay();
    }
    
    /**
     * Show progress overlay
     */
    showProgressOverlay(message, icon) {
        // Remove existing overlay
        this.hideProgressOverlay();
        
        const overlay = document.createElement('div');
        overlay.id = 'exportProgressOverlay';
        overlay.className = 'export-progress-overlay';
        
        overlay.innerHTML = `
            <div class="progress-content">
                <div class="progress-icon">
                    <i class="${icon}"></i>
                </div>
                <div class="progress-message">${message}</div>
                <div class="progress-spinner">
                    <div class="spinner"></div>
                </div>
            </div>
        `;
        
        // Style overlay
        Object.assign(overlay.style, {
            position: 'fixed',
            top: '0',
            left: '0',
            right: '0',
            bottom: '0',
            background: 'rgba(0, 0, 0, 0.7)',
            backdropFilter: 'blur(4px)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: '9999',
            color: 'white',
            fontSize: '16px',
            fontWeight: '500'
        });
        
        document.body.appendChild(overlay);
    }
    
    /**
     * Hide progress overlay
     */
    hideProgressOverlay() {
        const overlay = document.getElementById('exportProgressOverlay');
        if (overlay) {
            document.body.removeChild(overlay);
        }
    }
    
    /**
     * Update export controls UI
     */
    updateExportControls() {
        const exportQuality = document.getElementById('exportQuality');
        const exportFormat = document.getElementById('exportFormat');
        
        if (exportQuality) {
            exportQuality.value = this.exportSettings.quality;
        }
        
        if (exportFormat) {
            exportFormat.value = this.exportSettings.format;
        }
    }
    
    /**
     * Get export settings
     */
    getExportSettings() {
        return { ...this.exportSettings };
    }
    
    /**
     * Set export settings
     */
    setExportSettings(settings) {
        this.exportSettings = { ...this.exportSettings, ...settings };
        this.updateExportControls();
        this.emit('settings:updated', { settings: this.exportSettings });
    }
    
    /**
     * Get estimated file size
     */
    getEstimatedFileSize() {
        const width = this.canvas.width * this.exportSettings.multiplier;
        const height = this.canvas.height * this.exportSettings.multiplier;
        const pixels = width * height;
        
        // Rough estimation based on format
        let bytesPerPixel;
        switch (this.exportSettings.format) {
            case 'png':
                bytesPerPixel = 4; // RGBA
                break;
            case 'jpeg':
                bytesPerPixel = 1; // Compressed
                break;
            default:
                bytesPerPixel = 3;
        }
        
        const estimatedBytes = pixels * bytesPerPixel;
        return this.formatFileSize(estimatedBytes);
    }
    
    /**
     * Format file size for display
     */
    formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 Bytes';
        
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
    
    /**
     * Show toast notification
     */
    showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        const colors = {
            success: '#059669',
            error: '#dc2626',
            warning: '#d97706',
            info: '#2563eb'
        };
        
        Object.assign(toast.style, {
            position: 'fixed',
            bottom: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: colors[type] || colors.success,
            color: 'white',
            padding: '12px 24px',
            borderRadius: '8px',
            zIndex: '10000',
            fontSize: '14px',
            fontWeight: '500',
            boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 100);
        
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
    
    /**
     * Detect mobile device
     */
    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (navigator.maxTouchPoints && navigator.maxTouchPoints > 2);
    }
    
    /**
     * Event emitter
     */
    emit(eventName, data = {}) {
        const event = new CustomEvent(`export:${eventName}`, {
            detail: { ...data, exportManager: this }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * Event listener
     */
    on(eventName, handler) {
        document.addEventListener(`export:${eventName}`, handler);
    }
    
    /**
     * Remove event listener
     */
    off(eventName, handler) {
        document.removeEventListener(`export:${eventName}`, handler);
    }
}

// Export for global use
window.MobileExportManager = MobileExportManager;
