/* ========================================
   MOBILE RESPONSIVE HANDLER
   ======================================== */

/**
 * MobileResponsiveHandler - Handles responsive behavior and viewport management
 * Manages screen orientation, viewport scaling, and responsive UI adjustments
 */
class MobileResponsiveHandler {
    constructor(fabricCore) {
        this.core = fabricCore;
        this.canvas = fabricCore.getCanvas();
        
        // Viewport state
        this.viewport = {
            width: window.innerWidth,
            height: window.innerHeight,
            orientation: this.getOrientation(),
            isLandscape: window.innerWidth > window.innerHeight,
            devicePixelRatio: window.devicePixelRatio || 1
        };
        
        // Responsive breakpoints
        this.breakpoints = {
            mobile: 480,
            tablet: 768,
            desktop: 1024,
            large: 1200
        };
        
        // Current responsive state
        this.currentBreakpoint = this.getCurrentBreakpoint();
        this.isMobile = this.core.isMobileDevice();
        
        // Resize handling
        this.resizeTimeout = null;
        this.orientationTimeout = null;
        
        // Performance optimization
        this.rafId = null;
        this.pendingResize = false;
        
        this.init();
    }
    
    /**
     * Initialize responsive handler
     */
    init() {
        this.setupEventListeners();
        this.setupViewportMeta();
        this.handleInitialResize();
        
        console.log('MobileResponsiveHandler initialized');
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));
        
        // Orientation change
        window.addEventListener('orientationchange', () => {
            this.handleOrientationChange();
        });
        
        // Viewport change (for mobile browsers)
        if (this.isMobile) {
            window.addEventListener('scroll', this.throttle(() => {
                this.handleViewportChange();
            }, 100));
        }
        
        // Device pixel ratio change (for zoom)
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia(`(resolution: ${this.viewport.devicePixelRatio}dppx)`);
            mediaQuery.addListener(() => {
                this.handleDevicePixelRatioChange();
            });
        }
        
        // Keyboard events for mobile
        if (this.isMobile) {
            this.setupMobileKeyboardHandling();
        }
    }
    
    /**
     * Setup viewport meta tag for mobile
     */
    setupViewportMeta() {
        let viewportMeta = document.querySelector('meta[name="viewport"]');
        
        if (!viewportMeta) {
            viewportMeta = document.createElement('meta');
            viewportMeta.name = 'viewport';
            document.head.appendChild(viewportMeta);
        }
        
        // Set mobile-optimized viewport
        viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover';
    }
    
    /**
     * Setup mobile keyboard handling
     */
    setupMobileKeyboardHandling() {
        // Visual viewport API support
        if (window.visualViewport) {
            window.visualViewport.addEventListener('resize', () => {
                this.handleVisualViewportResize();
            });
        } else {
            // Fallback for older browsers
            let initialViewportHeight = window.innerHeight;
            
            window.addEventListener('resize', () => {
                const currentHeight = window.innerHeight;
                const heightDifference = initialViewportHeight - currentHeight;
                
                if (heightDifference > 150) {
                    // Likely keyboard is open
                    this.handleKeyboardOpen(heightDifference);
                } else {
                    // Keyboard likely closed
                    this.handleKeyboardClose();
                }
            });
        }
    }
    
    /**
     * Handle initial resize
     */
    handleInitialResize() {
        this.updateViewportState();
        this.applyResponsiveStyles();
        this.core.handleResize();
    }
    
    /**
     * Handle window resize
     */
    handleResize() {
        if (this.pendingResize) return;
        
        this.pendingResize = true;
        
        this.rafId = requestAnimationFrame(() => {
            this.updateViewportState();
            this.checkBreakpointChange();
            this.applyResponsiveStyles();
            this.core.handleResize();
            
            this.pendingResize = false;
            this.emit('resize', { viewport: this.viewport });
        });
    }
    
    /**
     * Handle orientation change
     */
    handleOrientationChange() {
        // Clear any pending timeouts
        if (this.orientationTimeout) {
            clearTimeout(this.orientationTimeout);
        }
        
        // Delay handling to allow browser to complete orientation change
        this.orientationTimeout = setTimeout(() => {
            this.updateViewportState();
            this.applyOrientationStyles();
            this.core.handleResize();
            
            this.emit('orientation:change', { 
                orientation: this.viewport.orientation,
                isLandscape: this.viewport.isLandscape 
            });
        }, 100);
    }
    
    /**
     * Handle viewport change (scroll, etc.)
     */
    handleViewportChange() {
        // Update viewport state for mobile browsers that change viewport on scroll
        this.updateViewportState();
        this.emit('viewport:change', { viewport: this.viewport });
    }
    
    /**
     * Handle device pixel ratio change
     */
    handleDevicePixelRatioChange() {
        this.viewport.devicePixelRatio = window.devicePixelRatio || 1;
        
        // Update canvas for new pixel ratio
        if (this.canvas) {
            this.canvas.enableRetinaScaling = true;
            this.canvas.renderAll();
        }
        
        this.emit('pixel-ratio:change', { devicePixelRatio: this.viewport.devicePixelRatio });
    }
    
    /**
     * Handle visual viewport resize (keyboard open/close)
     */
    handleVisualViewportResize() {
        const visualViewport = window.visualViewport;
        const heightDifference = window.innerHeight - visualViewport.height;
        
        if (heightDifference > 150) {
            this.handleKeyboardOpen(heightDifference);
        } else {
            this.handleKeyboardClose();
        }
    }
    
    /**
     * Handle keyboard open
     */
    handleKeyboardOpen(keyboardHeight) {
        document.body.classList.add('keyboard-open');
        
        // Adjust canvas container if needed
        const canvasContainer = document.getElementById('canvasContainer');
        if (canvasContainer && this.isMobile) {
            canvasContainer.style.maxHeight = `calc(100vh - ${keyboardHeight}px)`;
        }
        
        this.emit('keyboard:open', { keyboardHeight });
    }
    
    /**
     * Handle keyboard close
     */
    handleKeyboardClose() {
        document.body.classList.remove('keyboard-open');
        
        // Reset canvas container
        const canvasContainer = document.getElementById('canvasContainer');
        if (canvasContainer) {
            canvasContainer.style.maxHeight = '';
        }
        
        this.emit('keyboard:close');
    }
    
    /**
     * Update viewport state
     */
    updateViewportState() {
        this.viewport = {
            width: window.innerWidth,
            height: window.innerHeight,
            orientation: this.getOrientation(),
            isLandscape: window.innerWidth > window.innerHeight,
            devicePixelRatio: window.devicePixelRatio || 1
        };
    }
    
    /**
     * Check for breakpoint changes
     */
    checkBreakpointChange() {
        const newBreakpoint = this.getCurrentBreakpoint();
        
        if (newBreakpoint !== this.currentBreakpoint) {
            const oldBreakpoint = this.currentBreakpoint;
            this.currentBreakpoint = newBreakpoint;
            
            this.emit('breakpoint:change', { 
                from: oldBreakpoint, 
                to: newBreakpoint,
                viewport: this.viewport 
            });
        }
    }
    
    /**
     * Apply responsive styles
     */
    applyResponsiveStyles() {
        const body = document.body;
        
        // Remove existing breakpoint classes
        Object.keys(this.breakpoints).forEach(bp => {
            body.classList.remove(`bp-${bp}`);
        });
        
        // Add current breakpoint class
        body.classList.add(`bp-${this.currentBreakpoint}`);
        
        // Add orientation class
        body.classList.toggle('landscape', this.viewport.isLandscape);
        body.classList.toggle('portrait', !this.viewport.isLandscape);
        
        // Add mobile/desktop class
        body.classList.toggle('mobile-device', this.isMobile);
        body.classList.toggle('desktop-device', !this.isMobile);
    }
    
    /**
     * Apply orientation-specific styles
     */
    applyOrientationStyles() {
        const toolbar = document.getElementById('mobileToolbar');
        
        if (this.isMobile && toolbar) {
            if (this.viewport.isLandscape) {
                // Auto-collapse toolbar in landscape on mobile
                toolbar.classList.add('auto-collapsed');
            } else {
                // Expand toolbar in portrait
                toolbar.classList.remove('auto-collapsed');
            }
        }
    }
    
    /**
     * Get current orientation
     */
    getOrientation() {
        if (screen.orientation) {
            return screen.orientation.angle;
        } else if (window.orientation !== undefined) {
            return window.orientation;
        } else {
            return this.viewport.isLandscape ? 90 : 0;
        }
    }
    
    /**
     * Get current breakpoint
     */
    getCurrentBreakpoint() {
        const width = this.viewport.width;
        
        if (width >= this.breakpoints.large) {
            return 'large';
        } else if (width >= this.breakpoints.desktop) {
            return 'desktop';
        } else if (width >= this.breakpoints.tablet) {
            return 'tablet';
        } else {
            return 'mobile';
        }
    }
    
    /**
     * Check if current viewport matches breakpoint
     */
    isBreakpoint(breakpoint) {
        return this.currentBreakpoint === breakpoint;
    }
    
    /**
     * Check if current viewport is at least the specified breakpoint
     */
    isBreakpointUp(breakpoint) {
        const breakpointOrder = ['mobile', 'tablet', 'desktop', 'large'];
        const currentIndex = breakpointOrder.indexOf(this.currentBreakpoint);
        const targetIndex = breakpointOrder.indexOf(breakpoint);
        
        return currentIndex >= targetIndex;
    }
    
    /**
     * Check if current viewport is below the specified breakpoint
     */
    isBreakpointDown(breakpoint) {
        const breakpointOrder = ['mobile', 'tablet', 'desktop', 'large'];
        const currentIndex = breakpointOrder.indexOf(this.currentBreakpoint);
        const targetIndex = breakpointOrder.indexOf(breakpoint);
        
        return currentIndex < targetIndex;
    }
    
    /**
     * Get safe area insets (for devices with notches)
     */
    getSafeAreaInsets() {
        const style = getComputedStyle(document.documentElement);
        
        return {
            top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0'),
            right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0'),
            bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0'),
            left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0')
        };
    }
    
    /**
     * Apply safe area insets
     */
    applySafeAreaInsets() {
        const insets = this.getSafeAreaInsets();
        const root = document.documentElement;
        
        root.style.setProperty('--safe-top', `${insets.top}px`);
        root.style.setProperty('--safe-right', `${insets.right}px`);
        root.style.setProperty('--safe-bottom', `${insets.bottom}px`);
        root.style.setProperty('--safe-left', `${insets.left}px`);
    }
    
    /**
     * Optimize for touch devices
     */
    optimizeForTouch() {
        if (this.isMobile) {
            // Increase touch target sizes
            document.body.classList.add('touch-optimized');
            
            // Disable hover effects on touch devices
            const style = document.createElement('style');
            style.textContent = `
                @media (hover: none) and (pointer: coarse) {
                    *:hover {
                        /* Disable hover effects on touch devices */
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    /**
     * Debounce function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * Throttle function
     */
    throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
    
    /**
     * Get viewport information
     */
    getViewportInfo() {
        return {
            ...this.viewport,
            breakpoint: this.currentBreakpoint,
            safeAreaInsets: this.getSafeAreaInsets()
        };
    }
    
    /**
     * Force resize recalculation
     */
    forceResize() {
        this.handleResize();
    }
    
    /**
     * Event emitter
     */
    emit(eventName, data = {}) {
        const event = new CustomEvent(`responsive:${eventName}`, {
            detail: { ...data, responsiveHandler: this }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * Event listener
     */
    on(eventName, handler) {
        document.addEventListener(`responsive:${eventName}`, handler);
    }
    
    /**
     * Remove event listener
     */
    off(eventName, handler) {
        document.removeEventListener(`responsive:${eventName}`, handler);
    }
    
    /**
     * Cleanup
     */
    destroy() {
        if (this.rafId) {
            cancelAnimationFrame(this.rafId);
        }
        
        if (this.resizeTimeout) {
            clearTimeout(this.resizeTimeout);
        }
        
        if (this.orientationTimeout) {
            clearTimeout(this.orientationTimeout);
        }
    }
}

// Export for global use
window.MobileResponsiveHandler = MobileResponsiveHandler;
