/* ========================================
   MOBILE TOUCH HANDLER
   ======================================== */

/**
 * MobileTouchHandler - Advanced touch event handling for mobile devices
 * Handles gestures, multi-touch, and mobile-specific interactions
 */
class MobileTouchHandler {
    constructor(fabricCore) {
        this.core = fabricCore;
        this.canvas = fabricCore.getCanvas();
        
        // Touch state
        this.touches = new Map();
        this.gestureState = {
            isActive: false,
            type: null, // 'pan', 'pinch', 'rotate'
            startTime: 0,
            startDistance: 0,
            startAngle: 0,
            startCenter: { x: 0, y: 0 },
            lastCenter: { x: 0, y: 0 }
        };
        
        // Configuration
        this.config = {
            tapThreshold: 10, // pixels
            doubleTapThreshold: 300, // ms
            longPressThreshold: 500, // ms
            pinchThreshold: 10, // pixels
            rotationThreshold: 15, // degrees
            panThreshold: 5, // pixels
            maxTapDuration: 200 // ms
        };
        
        // State tracking
        this.lastTapTime = 0;
        this.lastTapPosition = { x: 0, y: 0 };
        this.longPressTimer = null;
        this.isLongPress = false;
        
        // Performance optimization
        this.rafId = null;
        this.pendingUpdate = false;
        
        this.init();
    }
    
    /**
     * Initialize touch handler
     */
    init() {
        if (!this.core.isMobileDevice()) {
            console.log('MobileTouchHandler: Not a mobile device, skipping initialization');
            return;
        }
        
        this.setupTouchEvents();
        this.setupGestureRecognition();
        
        console.log('MobileTouchHandler initialized');
    }
    
    /**
     * Setup touch event listeners
     */
    setupTouchEvents() {
        const canvasElement = this.canvas.upperCanvasEl;
        
        // Touch events
        canvasElement.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: false });
        canvasElement.addEventListener('touchmove', (e) => this.handleTouchMove(e), { passive: false });
        canvasElement.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: false });
        canvasElement.addEventListener('touchcancel', (e) => this.handleTouchCancel(e), { passive: false });
        
        // Prevent default behaviors
        canvasElement.addEventListener('gesturestart', (e) => e.preventDefault(), { passive: false });
        canvasElement.addEventListener('gesturechange', (e) => e.preventDefault(), { passive: false });
        canvasElement.addEventListener('gestureend', (e) => e.preventDefault(), { passive: false });
    }
    
    /**
     * Setup gesture recognition
     */
    setupGestureRecognition() {
        // Listen for canvas events to coordinate with touch handling
        this.canvas.on('mouse:down', (e) => {
            if (e.e.touches) {
                this.handleCanvasMouseDown(e);
            }
        });
        
        this.canvas.on('mouse:move', (e) => {
            if (e.e.touches) {
                this.handleCanvasMouseMove(e);
            }
        });
        
        this.canvas.on('mouse:up', (e) => {
            if (e.e.changedTouches) {
                this.handleCanvasMouseUp(e);
            }
        });
    }
    
    /**
     * Handle touch start
     */
    handleTouchStart(e) {
        e.preventDefault();
        
        const touches = Array.from(e.touches);
        const currentTime = Date.now();
        
        // Store touch information
        touches.forEach(touch => {
            this.touches.set(touch.identifier, {
                id: touch.identifier,
                startX: touch.clientX,
                startY: touch.clientY,
                currentX: touch.clientX,
                currentY: touch.clientY,
                startTime: currentTime
            });
        });
        
        // Handle different touch counts
        switch (touches.length) {
            case 1:
                this.handleSingleTouchStart(touches[0], currentTime);
                break;
            case 2:
                this.handleMultiTouchStart(touches, currentTime);
                break;
            default:
                this.cancelAllGestures();
                break;
        }
        
        this.emit('touch:start', { touches: touches.length, event: e });
    }
    
    /**
     * Handle single touch start
     */
    handleSingleTouchStart(touch, currentTime) {
        const position = { x: touch.clientX, y: touch.clientY };
        
        // Check for double tap
        const timeDiff = currentTime - this.lastTapTime;
        const distance = this.getDistance(position, this.lastTapPosition);
        
        if (timeDiff < this.config.doubleTapThreshold && distance < this.config.tapThreshold) {
            this.handleDoubleTap(position);
            return;
        }
        
        // Start long press timer
        this.startLongPressTimer(position);
        
        // Update last tap info
        this.lastTapTime = currentTime;
        this.lastTapPosition = position;
    }
    
    /**
     * Handle multi-touch start
     */
    handleMultiTouchStart(touches, currentTime) {
        this.cancelLongPress();
        
        if (touches.length === 2) {
            const touch1 = touches[0];
            const touch2 = touches[1];
            
            this.gestureState = {
                isActive: true,
                type: 'multi',
                startTime: currentTime,
                startDistance: this.getDistance(
                    { x: touch1.clientX, y: touch1.clientY },
                    { x: touch2.clientX, y: touch2.clientY }
                ),
                startAngle: this.getAngle(
                    { x: touch1.clientX, y: touch1.clientY },
                    { x: touch2.clientX, y: touch2.clientY }
                ),
                startCenter: this.getCenter(
                    { x: touch1.clientX, y: touch1.clientY },
                    { x: touch2.clientX, y: touch2.clientY }
                ),
                lastCenter: this.getCenter(
                    { x: touch1.clientX, y: touch1.clientY },
                    { x: touch2.clientX, y: touch2.clientY }
                )
            };
        }
    }
    
    /**
     * Handle touch move
     */
    handleTouchMove(e) {
        e.preventDefault();
        
        const touches = Array.from(e.touches);
        
        // Update touch positions
        touches.forEach(touch => {
            const storedTouch = this.touches.get(touch.identifier);
            if (storedTouch) {
                storedTouch.currentX = touch.clientX;
                storedTouch.currentY = touch.clientY;
            }
        });
        
        // Cancel long press if touch moved too much
        if (touches.length === 1) {
            const touch = touches[0];
            const storedTouch = this.touches.get(touch.identifier);
            if (storedTouch) {
                const distance = this.getDistance(
                    { x: storedTouch.startX, y: storedTouch.startY },
                    { x: touch.clientX, y: touch.clientY }
                );
                
                if (distance > this.config.tapThreshold) {
                    this.cancelLongPress();
                    
                    // Start pan gesture if not already active
                    if (!this.gestureState.isActive) {
                        this.startPanGesture(touch);
                    } else if (this.gestureState.type === 'pan') {
                        this.updatePanGesture(touch);
                    }
                }
            }
        } else if (touches.length === 2 && this.gestureState.isActive) {
            this.handleMultiTouchMove(touches);
        }
        
        this.emit('touch:move', { touches: touches.length, event: e });
    }
    
    /**
     * Handle multi-touch move
     */
    handleMultiTouchMove(touches) {
        const touch1 = touches[0];
        const touch2 = touches[1];
        
        const currentDistance = this.getDistance(
            { x: touch1.clientX, y: touch1.clientY },
            { x: touch2.clientX, y: touch2.clientY }
        );
        
        const currentAngle = this.getAngle(
            { x: touch1.clientX, y: touch1.clientY },
            { x: touch2.clientX, y: touch2.clientY }
        );
        
        const currentCenter = this.getCenter(
            { x: touch1.clientX, y: touch1.clientY },
            { x: touch2.clientX, y: touch2.clientY }
        );
        
        // Determine gesture type
        const distanceDiff = Math.abs(currentDistance - this.gestureState.startDistance);
        const angleDiff = Math.abs(currentAngle - this.gestureState.startAngle);
        
        if (distanceDiff > this.config.pinchThreshold) {
            this.handlePinchGesture(currentDistance, currentCenter);
        }
        
        if (angleDiff > this.config.rotationThreshold) {
            this.handleRotationGesture(currentAngle, currentCenter);
        }
        
        // Update last center for pan detection
        this.gestureState.lastCenter = currentCenter;
    }
    
    /**
     * Handle touch end
     */
    handleTouchEnd(e) {
        e.preventDefault();
        
        const changedTouches = Array.from(e.changedTouches);
        const remainingTouches = Array.from(e.touches);
        
        // Remove ended touches
        changedTouches.forEach(touch => {
            const storedTouch = this.touches.get(touch.identifier);
            if (storedTouch) {
                this.handleTouchEndForTouch(storedTouch, touch);
                this.touches.delete(touch.identifier);
            }
        });
        
        // Handle gesture end
        if (remainingTouches.length === 0) {
            this.endAllGestures();
        } else if (remainingTouches.length === 1 && this.gestureState.isActive) {
            // Transition from multi-touch to single touch
            this.gestureState.isActive = false;
        }
        
        this.emit('touch:end', { touches: remainingTouches.length, event: e });
    }
    
    /**
     * Handle touch end for specific touch
     */
    handleTouchEndForTouch(storedTouch, endTouch) {
        const duration = Date.now() - storedTouch.startTime;
        const distance = this.getDistance(
            { x: storedTouch.startX, y: storedTouch.startY },
            { x: endTouch.clientX, y: endTouch.clientY }
        );
        
        // Check for tap
        if (duration < this.config.maxTapDuration && distance < this.config.tapThreshold && !this.isLongPress) {
            this.handleTap({ x: endTouch.clientX, y: endTouch.clientY });
        }
        
        this.cancelLongPress();
    }
    
    /**
     * Handle touch cancel
     */
    handleTouchCancel(e) {
        e.preventDefault();
        this.cancelAllGestures();
        this.touches.clear();
        this.emit('touch:cancel', { event: e });
    }
    
    /**
     * Start pan gesture
     */
    startPanGesture(touch) {
        this.gestureState = {
            isActive: true,
            type: 'pan',
            startTime: Date.now(),
            startCenter: { x: touch.clientX, y: touch.clientY },
            lastCenter: { x: touch.clientX, y: touch.clientY }
        };
        
        this.emit('gesture:pan:start', { center: this.gestureState.startCenter });
    }
    
    /**
     * Update pan gesture
     */
    updatePanGesture(touch) {
        const currentCenter = { x: touch.clientX, y: touch.clientY };
        const deltaX = currentCenter.x - this.gestureState.lastCenter.x;
        const deltaY = currentCenter.y - this.gestureState.lastCenter.y;
        
        // Only pan if no object is selected or if explicitly enabled
        const activeObject = this.canvas.getActiveObject();
        if (!activeObject) {
            this.core.panCanvas(deltaX, deltaY);
        }
        
        this.gestureState.lastCenter = currentCenter;
        this.emit('gesture:pan:update', { delta: { x: deltaX, y: deltaY }, center: currentCenter });
    }
    
    /**
     * Handle pinch gesture
     */
    handlePinchGesture(currentDistance, center) {
        const scale = currentDistance / this.gestureState.startDistance;
        const newZoom = this.core.currentZoom * scale;
        
        // Convert screen coordinates to canvas coordinates
        const canvasRect = this.canvas.upperCanvasEl.getBoundingClientRect();
        const canvasPoint = {
            x: center.x - canvasRect.left,
            y: center.y - canvasRect.top
        };
        
        this.core.setZoom(newZoom, new fabric.Point(canvasPoint.x, canvasPoint.y));
        
        // Update start distance for next calculation
        this.gestureState.startDistance = currentDistance;
        
        this.emit('gesture:pinch', { scale, zoom: newZoom, center });
    }
    
    /**
     * Handle rotation gesture
     */
    handleRotationGesture(currentAngle, center) {
        const angleDelta = currentAngle - this.gestureState.startAngle;
        
        // Apply rotation to selected object if any
        const activeObject = this.canvas.getActiveObject();
        if (activeObject && activeObject.type !== 'activeSelection') {
            const newAngle = activeObject.angle + angleDelta;
            activeObject.set('angle', newAngle);
            this.canvas.renderAll();
        }
        
        // Update start angle for next calculation
        this.gestureState.startAngle = currentAngle;
        
        this.emit('gesture:rotate', { angle: angleDelta, center });
    }
    
    /**
     * Handle tap
     */
    handleTap(position) {
        // Convert screen coordinates to canvas coordinates
        const canvasRect = this.canvas.upperCanvasEl.getBoundingClientRect();
        const canvasPoint = {
            x: position.x - canvasRect.left,
            y: position.y - canvasRect.top
        };
        
        this.emit('gesture:tap', { position: canvasPoint, screenPosition: position });
    }
    
    /**
     * Handle double tap
     */
    handleDoubleTap(position) {
        // Convert screen coordinates to canvas coordinates
        const canvasRect = this.canvas.upperCanvasEl.getBoundingClientRect();
        const canvasPoint = {
            x: position.x - canvasRect.left,
            y: position.y - canvasRect.top
        };
        
        // Zoom to fit or reset zoom
        if (this.core.currentZoom > this.core.scalingData.scaleRatio) {
            this.core.resetZoom();
        } else {
            this.core.setZoom(this.core.currentZoom * 2, new fabric.Point(canvasPoint.x, canvasPoint.y));
        }
        
        this.emit('gesture:double-tap', { position: canvasPoint, screenPosition: position });
    }
    
    /**
     * Start long press timer
     */
    startLongPressTimer(position) {
        this.cancelLongPress();
        
        this.longPressTimer = setTimeout(() => {
            this.isLongPress = true;
            this.handleLongPress(position);
        }, this.config.longPressThreshold);
    }
    
    /**
     * Handle long press
     */
    handleLongPress(position) {
        // Convert screen coordinates to canvas coordinates
        const canvasRect = this.canvas.upperCanvasEl.getBoundingClientRect();
        const canvasPoint = {
            x: position.x - canvasRect.left,
            y: position.y - canvasRect.top
        };
        
        this.emit('gesture:long-press', { position: canvasPoint, screenPosition: position });
    }
    
    /**
     * Cancel long press
     */
    cancelLongPress() {
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
        this.isLongPress = false;
    }
    
    /**
     * End all gestures
     */
    endAllGestures() {
        if (this.gestureState.isActive) {
            this.emit(`gesture:${this.gestureState.type}:end`, { gestureState: this.gestureState });
        }
        
        this.gestureState.isActive = false;
        this.gestureState.type = null;
        this.cancelLongPress();
    }
    
    /**
     * Cancel all gestures
     */
    cancelAllGestures() {
        this.endAllGestures();
        this.touches.clear();
    }
    
    /**
     * Handle canvas mouse events (for coordination)
     */
    handleCanvasMouseDown(e) {
        // Coordinate with Fabric.js mouse handling
    }
    
    handleCanvasMouseMove(e) {
        // Coordinate with Fabric.js mouse handling
    }
    
    handleCanvasMouseUp(e) {
        // Coordinate with Fabric.js mouse handling
    }
    
    /**
     * Utility functions
     */
    getDistance(point1, point2) {
        const dx = point1.x - point2.x;
        const dy = point1.y - point2.y;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    getAngle(point1, point2) {
        return Math.atan2(point2.y - point1.y, point2.x - point1.x) * 180 / Math.PI;
    }
    
    getCenter(point1, point2) {
        return {
            x: (point1.x + point2.x) / 2,
            y: (point1.y + point2.y) / 2
        };
    }
    
    /**
     * Event emitter
     */
    emit(eventName, data = {}) {
        const event = new CustomEvent(`touch:${eventName}`, {
            detail: { ...data, touchHandler: this }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * Event listener
     */
    on(eventName, handler) {
        document.addEventListener(`touch:${eventName}`, handler);
    }
    
    /**
     * Remove event listener
     */
    off(eventName, handler) {
        document.removeEventListener(`touch:${eventName}`, handler);
    }
}

// Export for global use
window.MobileTouchHandler = MobileTouchHandler;
