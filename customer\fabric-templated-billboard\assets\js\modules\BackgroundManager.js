/**
 * BackgroundManager - Handles background image selection and replacement
 * Follows single responsibility principle - only manages background changes
 */
class BackgroundManager {
    constructor(canvasManager, templateManager) {
        this.canvasManager = canvasManager;
        this.templateManager = templateManager;
        this.modal = null;
        this.currentCategory = null;
        this.init();
    }

    /**
     * Initialize background manager
     */
    init() {
        this.modal = document.getElementById('backgroundModal');
        this.bindEvents();
    }

    /**
     * Bind event handlers
     */
    bindEvents() {
        // Change background button
        const changeBtn = document.getElementById('changeBackgroundBtn');
        if (changeBtn) {
            changeBtn.addEventListener('click', () => this.showBackgroundModal());
        }

        // Close modal button
        const closeBtn = document.getElementById('closeBackgroundModal');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hideBackgroundModal());
        }

        // Modal overlay click to close
        if (this.modal) {
            const overlay = this.modal.querySelector('.modal-overlay');
            if (overlay) {
                overlay.addEventListener('click', () => this.hideBackgroundModal());
            }
        }

        console.log('✅ BackgroundManager events bound');
    }

    /**
     * Show background selection modal
     */
    showBackgroundModal() {
        if (!this.currentCategory) {
            this.showError('Please load a template first to change background');
            return;
        }

        this.populateBackgroundGrid();

        if (this.modal) {
            this.modal.style.display = 'flex';
            // Add animation class after display
            setTimeout(() => {
                this.modal.classList.add('show');
            }, 10);
        }

        console.log('🎨 Background modal opened');
    }

    /**
     * Hide background selection modal
     */
    hideBackgroundModal() {
        if (this.modal) {
            this.modal.classList.remove('show');
            setTimeout(() => {
                this.modal.style.display = 'none';
            }, 200);
        }
    }

    /**
     * Populate background grid with available backgrounds
     */
    populateBackgroundGrid() {
        const grid = document.getElementById('backgroundGrid');
        if (!grid) return;

        grid.innerHTML = '';

        // Get backgrounds for current category
        const backgrounds = this.getBackgroundsForCategory(this.currentCategory);

        if (!backgrounds || backgrounds.length === 0) {
            grid.innerHTML = '<p style="text-align: center; color: #666;">No background images available for this category.</p>';
            return;
        }

        backgrounds.forEach(bg => {
            const option = document.createElement('div');
            option.className = 'background-option';
            option.style.backgroundImage = `url('${bg.url}')`;
            option.title = bg.name;
            
            option.addEventListener('click', () => {
                this.selectBackground(bg.url);
            });

            grid.appendChild(option);
        });

        console.log(`📋 Populated ${backgrounds.length} background options`);
    }

    /**
     * Select and apply background
     */
    selectBackground(imageUrl) {
        console.log('🎨 Applying background:', imageUrl);

        // Use CanvasManager's proper background scaling method
        this.canvasManager.setBackgroundImage(imageUrl, (img) => {
            if (img) {
                console.log('✅ Background applied successfully with proper scaling');

                // Trigger success event
                document.dispatchEvent(new CustomEvent('background:changed', {
                    detail: { success: true, message: 'Background changed successfully!' }
                }));
            } else {
                console.error('❌ Failed to apply background');

                // Trigger error event
                document.dispatchEvent(new CustomEvent('background:error', {
                    detail: { success: false, message: 'Failed to load background image' }
                }));
            }
        });

        this.hideBackgroundModal();
    }

    /**
     * Get backgrounds for category (using template-manager data)
     */
    getBackgroundsForCategory(category) {
        // Use the existing template manager's background data
        if (this.templateManager && this.templateManager.getBackgroundsForCategory) {
            return this.templateManager.getBackgroundsForCategory(category);
        }

        // Fallback: generate backgrounds from template specs
        return this.generateBackgroundsFromTemplates(category);
    }

    /**
     * Generate background options from existing templates
     */
    generateBackgroundsFromTemplates(category) {
        const templates = window.TEMPLATE_SPECS?.[category] || {};
        const backgrounds = [];
        
        Object.entries(templates).forEach(([id, template]) => {
            if (template.background) {
                backgrounds.push({
                    id: `${category}-${id}`,
                    url: template.background,
                    name: `${category} Background ${backgrounds.length + 1}`
                });
            }
        });

        return backgrounds;
    }

    /**
     * Set current category
     */
    setCurrentCategory(category) {
        this.currentCategory = category;
        console.log('📂 Background manager category set to:', category);
    }

    /**
     * Show background change section
     */
    showBackgroundSection() {
        const section = document.getElementById('backgroundChangeSection');
        if (section) {
            section.style.display = 'block';
        }
    }

    /**
     * Hide background change section
     */
    hideBackgroundSection() {
        const section = document.getElementById('backgroundChangeSection');
        if (section) {
            section.style.display = 'none';
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        document.dispatchEvent(new CustomEvent('background:error', {
            detail: { message }
        }));
    }

    /**
     * Clear background manager
     */
    clear() {
        this.currentCategory = null;
        this.hideBackgroundModal();
        this.hideBackgroundSection();
    }
}

// Export for use in main application
window.BackgroundManager = BackgroundManager;
