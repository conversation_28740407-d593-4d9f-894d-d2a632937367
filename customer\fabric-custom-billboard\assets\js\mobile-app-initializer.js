/* ========================================
   MOBILE APP INITIALIZER
   ======================================== */

/**
 * MobileAppInitializer - Main application initializer
 * Coordinates all components and manages the application lifecycle
 */
class MobileAppInitializer {
    constructor() {
        // Core components
        this.fabricCore = null;
        this.canvasManager = null;
        this.toolbarManager = null;
        this.textManager = null;
        this.backgroundManager = null;
        this.exportManager = null;
        this.touchHandler = null;
        this.responsiveHandler = null;
        
        // Application state
        this.isInitialized = false;
        this.isLoading = true;
        this.initializationSteps = [
            'Loading Fabric.js',
            'Initializing Canvas',
            'Setting up Managers',
            'Configuring Touch Events',
            'Applying Responsive Design',
            'Finalizing Setup'
        ];
        this.currentStep = 0;
        
        // Error handling
        this.errors = [];
        this.maxRetries = 3;
        this.retryCount = 0;
        
        this.init();
    }
    
    /**
     * Initialize the application
     */
    async init() {
        try {
            console.log('🚀 Starting Mobile Billboard Editor initialization...');
            
            // Show loading screen
            this.showLoadingScreen();
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }
            
            // Initialize components step by step
            await this.initializeComponents();
            
            // Setup global event coordination
            this.setupGlobalEventHandling();
            
            // Setup error handling
            this.setupErrorHandling();
            
            // Hide loading screen
            this.hideLoadingScreen();
            
            this.isInitialized = true;
            this.isLoading = false;
            
            console.log('✅ Mobile Billboard Editor initialized successfully!');
            this.emit('app:initialized');
            
        } catch (error) {
            console.error('❌ Failed to initialize Mobile Billboard Editor:', error);
            this.handleInitializationError(error);
        }
    }
    
    /**
     * Initialize all components
     */
    async initializeComponents() {
        // Step 1: Initialize Fabric.js Core
        this.updateLoadingStep(0);
        await this.initializeFabricCore();
        
        // Step 2: Initialize Canvas Manager
        this.updateLoadingStep(1);
        await this.initializeCanvasManager();
        
        // Step 3: Initialize UI Managers
        this.updateLoadingStep(2);
        await this.initializeUIManagers();
        
        // Step 4: Initialize Touch Handler
        this.updateLoadingStep(3);
        await this.initializeTouchHandler();
        
        // Step 5: Initialize Responsive Handler
        this.updateLoadingStep(4);
        await this.initializeResponsiveHandler();
        
        // Step 6: Final setup
        this.updateLoadingStep(5);
        await this.finalizeSetup();
    }
    
    /**
     * Initialize Fabric.js core
     */
    async initializeFabricCore() {
        return new Promise((resolve, reject) => {
            try {
                console.log('🔄 Initializing Fabric.js core...');

                // Check if Fabric.js is loaded
                if (typeof fabric === 'undefined') {
                    throw new Error('Fabric.js library not loaded');
                }
                console.log('✅ Fabric.js library confirmed available');

                // Check if required DOM elements exist
                const canvasElement = document.getElementById('fabricCanvas');
                const containerElement = document.getElementById('canvasContainer');

                if (!canvasElement) {
                    throw new Error('Canvas element with ID "fabricCanvas" not found in DOM');
                }
                if (!containerElement) {
                    throw new Error('Container element with ID "canvasContainer" not found in DOM');
                }
                console.log('✅ Required DOM elements found');

                // Initialize core with shorter timeout and better error handling
                const initTimeout = setTimeout(() => {
                    console.error('❌ Fabric.js core initialization timed out after 5 seconds');
                    console.log('🔄 Attempting direct initialization...');

                    // Try direct initialization as fallback
                    try {
                        if (this.fabricCore && this.fabricCore.canvas) {
                            console.log('✅ Canvas exists, considering initialization successful');
                            resolve();
                        } else {
                            reject(new Error('Fabric.js core initialization timed out and fallback failed'));
                        }
                    } catch (fallbackError) {
                        reject(new Error(`Initialization timeout and fallback failed: ${fallbackError.message}`));
                    }
                }, 5000);

                // Create the core instance
                console.log('🔄 Creating MobileFabricCore instance...');
                this.fabricCore = new MobileFabricCore('fabricCanvas', {
                    width: 800,
                    height: 400,
                    backgroundColor: '#ffffff'
                });

                // Check if it was created successfully
                if (!this.fabricCore) {
                    clearTimeout(initTimeout);
                    throw new Error('Failed to create MobileFabricCore instance');
                }

                // Wait for initialization with multiple event listeners
                let resolved = false;

                const handleSuccess = () => {
                    if (!resolved) {
                        resolved = true;
                        clearTimeout(initTimeout);
                        console.log('✅ Fabric.js core initialized successfully');
                        resolve();
                    }
                };

                const handleError = (e) => {
                    if (!resolved) {
                        resolved = true;
                        clearTimeout(initTimeout);
                        const error = e.detail ? e.detail.error : e;
                        console.error('❌ Fabric.js core initialization error:', error);
                        reject(error);
                    }
                };

                // Listen for events
                this.fabricCore.on('initialized', handleSuccess);
                this.fabricCore.on('error', handleError);

                // Also check if it's already initialized (in case event was missed)
                setTimeout(() => {
                    if (!resolved && this.fabricCore && this.fabricCore.isInitialized) {
                        console.log('✅ Canvas was already initialized, proceeding...');
                        handleSuccess();
                    }
                }, 100);

            } catch (error) {
                console.error('❌ Failed to initialize Fabric.js core:', error);
                reject(error);
            }
        });
    }
    
    /**
     * Initialize canvas manager
     */
    async initializeCanvasManager() {
        return new Promise((resolve) => {
            this.canvasManager = new MobileCanvasManager(this.fabricCore);
            console.log('✅ Canvas manager initialized');
            resolve();
        });
    }
    
    /**
     * Initialize UI managers
     */
    async initializeUIManagers() {
        return new Promise((resolve) => {
            // Initialize toolbar manager
            this.toolbarManager = new MobileToolbarManager(this.canvasManager);
            
            // Initialize text manager
            this.textManager = new MobileTextManager(this.canvasManager);
            
            // Initialize background manager
            this.backgroundManager = new MobileBackgroundManager(this.canvasManager);
            
            // Initialize export manager
            this.exportManager = new MobileExportManager(this.canvasManager);
            
            console.log('✅ UI managers initialized');
            resolve();
        });
    }
    
    /**
     * Initialize touch handler
     */
    async initializeTouchHandler() {
        return new Promise((resolve) => {
            this.touchHandler = new MobileTouchHandler(this.fabricCore);
            console.log('✅ Touch handler initialized');
            resolve();
        });
    }
    
    /**
     * Initialize responsive handler
     */
    async initializeResponsiveHandler() {
        return new Promise((resolve) => {
            this.responsiveHandler = new MobileResponsiveHandler(this.fabricCore);
            console.log('✅ Responsive handler initialized');
            resolve();
        });
    }
    
    /**
     * Finalize setup
     */
    async finalizeSetup() {
        return new Promise((resolve) => {
            // Setup keyboard shortcuts
            this.setupKeyboardShortcuts();
            
            // Setup auto-save
            this.setupAutoSave();
            
            // Setup performance monitoring
            this.setupPerformanceMonitoring();
            
            // Apply initial optimizations
            this.applyInitialOptimizations();
            
            console.log('✅ Final setup completed');
            resolve();
        });
    }
    
    /**
     * Setup global event handling
     */
    setupGlobalEventHandling() {
        // Coordinate between managers
        this.setupManagerCoordination();
        
        // Setup global shortcuts
        this.setupGlobalShortcuts();
        
        // Setup window events
        this.setupWindowEvents();
    }
    
    /**
     * Setup manager coordination
     */
    setupManagerCoordination() {
        // Toolbar to background manager
        this.toolbarManager.on('background:choose', () => {
            this.backgroundManager.openModal();
        });
        
        // Toolbar to export manager
        this.toolbarManager.on('export:requested', () => {
            this.exportManager.exportImage();
        });
        
        this.toolbarManager.on('checkout:requested', () => {
            this.exportManager.initiateCheckout();
        });
        
        // Text manager to toolbar coordination
        this.textManager.on('text:selected', (e) => {
            this.toolbarManager.handleSelectionChange(e.detail.textObjects);
        });
        
        // Canvas events to all managers
        this.canvasManager.on('selection:changed', (e) => {
            this.emit('global:selection:changed', e.detail);
        });
    }
    
    /**
     * Setup keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Only handle shortcuts when not editing text
            if (this.textManager.isEditing) return;
            
            // Ctrl/Cmd + Z - Undo
            if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {
                e.preventDefault();
                this.canvasManager.undo();
            }
            
            // Ctrl/Cmd + Shift + Z - Redo
            if ((e.ctrlKey || e.metaKey) && e.key === 'z' && e.shiftKey) {
                e.preventDefault();
                this.canvasManager.redo();
            }
            
            // Ctrl/Cmd + D - Duplicate
            if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
                e.preventDefault();
                this.canvasManager.duplicateSelected();
            }
            
            // Delete key - Delete selected
            if (e.key === 'Delete' || e.key === 'Backspace') {
                e.preventDefault();
                this.canvasManager.deleteSelected();
            }
            
            // Ctrl/Cmd + A - Select all
            if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
                e.preventDefault();
                this.selectAllObjects();
            }
            
            // Escape - Deselect all
            if (e.key === 'Escape') {
                this.fabricCore.canvas.discardActiveObject();
                this.fabricCore.canvas.renderAll();
            }
        });
    }
    
    /**
     * Setup global shortcuts
     */
    setupGlobalShortcuts() {
        // Quick add text
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 't') {
                e.preventDefault();
                this.textManager.createText();
            }
        });
    }
    
    /**
     * Setup window events
     */
    setupWindowEvents() {
        // Prevent accidental page refresh
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges()) {
                e.preventDefault();
                e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
                return e.returnValue;
            }
        });
        
        // Handle visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.handleAppHidden();
            } else {
                this.handleAppVisible();
            }
        });
    }
    
    /**
     * Setup auto-save
     */
    setupAutoSave() {
        setInterval(() => {
            this.autoSave();
        }, 30000); // Auto-save every 30 seconds
    }
    
    /**
     * Setup performance monitoring
     */
    setupPerformanceMonitoring() {
        // Monitor canvas performance
        let frameCount = 0;
        let lastTime = performance.now();
        
        const monitorPerformance = () => {
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - lastTime >= 1000) {
                const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                this.emit('performance:fps', { fps });
                
                frameCount = 0;
                lastTime = currentTime;
            }
            
            requestAnimationFrame(monitorPerformance);
        };
        
        requestAnimationFrame(monitorPerformance);
    }
    
    /**
     * Apply initial optimizations
     */
    applyInitialOptimizations() {
        // Optimize for mobile
        if (this.fabricCore.isMobileDevice()) {
            // Reduce canvas quality for better performance on mobile
            this.fabricCore.canvas.enableRetinaScaling = false;
            
            // Optimize object caching
            fabric.Object.prototype.objectCaching = true;
            fabric.Object.prototype.statefullCache = true;
        }
    }
    
    /**
     * Setup error handling
     */
    setupErrorHandling() {
        // Global error handler
        window.addEventListener('error', (e) => {
            this.handleError(e.error);
        });
        
        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (e) => {
            this.handleError(e.reason);
        });
    }
    
    /**
     * Handle initialization error
     */
    handleInitializationError(error) {
        this.errors.push(error);
        
        if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            console.log(`🔄 Retrying initialization (${this.retryCount}/${this.maxRetries})...`);
            
            setTimeout(() => {
                this.init();
            }, 1000 * this.retryCount);
        } else {
            this.showErrorScreen(error);
        }
    }
    
    /**
     * Handle runtime error
     */
    handleError(error) {
        console.error('Runtime error:', error);
        this.errors.push(error);
        this.emit('app:error', { error });
    }
    
    /**
     * Show loading screen
     */
    showLoadingScreen() {
        const overlay = document.getElementById('canvasOverlay');
        if (overlay) {
            overlay.classList.remove('hidden');
        }
    }
    
    /**
     * Hide loading screen
     */
    hideLoadingScreen() {
        const overlay = document.getElementById('canvasOverlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }
    
    /**
     * Update loading step
     */
    updateLoadingStep(stepIndex) {
        this.currentStep = stepIndex;
        const loadingText = document.querySelector('#canvasLoading span');
        if (loadingText && this.initializationSteps[stepIndex]) {
            loadingText.textContent = this.initializationSteps[stepIndex];
        }
    }
    
    /**
     * Show error screen
     */
    showErrorScreen(error) {
        const overlay = document.getElementById('canvasOverlay');
        if (overlay) {
            overlay.innerHTML = `
                <div class="error-content">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3>Failed to Initialize</h3>
                    <p>The billboard editor failed to load. Please refresh the page to try again.</p>
                    <button onclick="window.location.reload()" class="retry-btn">
                        <i class="fas fa-redo"></i> Retry
                    </button>
                </div>
            `;
            overlay.classList.remove('hidden');
        }
    }
    
    /**
     * Auto-save functionality
     */
    autoSave() {
        if (!this.isInitialized || !this.canvasManager) return;
        
        try {
            const canvasData = this.canvasManager.exportAsJSON();
            localStorage.setItem('billboard_autosave', JSON.stringify({
                data: canvasData,
                timestamp: Date.now()
            }));
        } catch (error) {
            console.warn('Auto-save failed:', error);
        }
    }
    
    /**
     * Load auto-saved data
     */
    loadAutoSave() {
        try {
            const saved = localStorage.getItem('billboard_autosave');
            if (saved) {
                const { data, timestamp } = JSON.parse(saved);
                
                // Only load if saved within last 24 hours
                if (Date.now() - timestamp < 24 * 60 * 60 * 1000) {
                    return data;
                }
            }
        } catch (error) {
            console.warn('Failed to load auto-save:', error);
        }
        
        return null;
    }
    
    /**
     * Check for unsaved changes
     */
    hasUnsavedChanges() {
        // Simple check - in a real app, you'd compare with last saved state
        return this.canvasManager && this.canvasManager.getAllObjects().length > 0;
    }
    
    /**
     * Select all objects
     */
    selectAllObjects() {
        const objects = this.fabricCore.canvas.getObjects().filter(obj => !obj.isBackground);
        if (objects.length > 0) {
            const selection = new fabric.ActiveSelection(objects, {
                canvas: this.fabricCore.canvas
            });
            this.fabricCore.canvas.setActiveObject(selection);
            this.fabricCore.canvas.renderAll();
        }
    }
    
    /**
     * Handle app hidden
     */
    handleAppHidden() {
        // Pause any animations or heavy operations
        this.emit('app:hidden');
    }
    
    /**
     * Handle app visible
     */
    handleAppVisible() {
        // Resume operations
        this.emit('app:visible');
    }
    
    /**
     * Get application state
     */
    getAppState() {
        return {
            isInitialized: this.isInitialized,
            isLoading: this.isLoading,
            currentStep: this.currentStep,
            errors: this.errors,
            hasUnsavedChanges: this.hasUnsavedChanges()
        };
    }
    
    /**
     * Destroy application
     */
    destroy() {
        // Cleanup all components
        if (this.responsiveHandler) {
            this.responsiveHandler.destroy();
        }
        
        if (this.fabricCore) {
            this.fabricCore.destroy();
        }
        
        // Clear auto-save
        localStorage.removeItem('billboard_autosave');
        
        this.emit('app:destroyed');
    }
    
    /**
     * Event emitter
     */
    emit(eventName, data = {}) {
        const event = new CustomEvent(`app:${eventName}`, {
            detail: { ...data, app: this }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * Event listener
     */
    on(eventName, handler) {
        document.addEventListener(`app:${eventName}`, handler);
    }
    
    /**
     * Remove event listener
     */
    off(eventName, handler) {
        document.removeEventListener(`app:${eventName}`, handler);
    }
}

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Create global app instance
    window.MobileBillboardApp = new MobileAppInitializer();
    
    // Expose app for debugging
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        window.app = window.MobileBillboardApp;
        console.log('🔧 Debug mode: App instance available as window.app');
    }
});

// Export for global use
window.MobileAppInitializer = MobileAppInitializer;
