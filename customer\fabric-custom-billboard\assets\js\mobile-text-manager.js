/* ========================================
   MOBILE TEXT MANAGER
   ======================================== */

/**
 * MobileTextManager - Advanced text editing functionality for mobile devices
 * Handles text creation, editing, formatting, and mobile-specific interactions
 */
class MobileTextManager {
    constructor(canvasManager) {
        this.canvasManager = canvasManager;
        this.canvas = canvasManager.canvas;
        
        // Text editing state
        this.isEditing = false;
        this.currentTextObject = null;
        this.editingTimeout = null;
        
        // Mobile-specific settings
        this.isMobile = this.detectMobile();
        this.touchEditDelay = 300; // ms delay for touch edit
        this.doubleTapThreshold = 300; // ms for double tap detection
        this.lastTapTime = 0;
        
        // Font management
        this.availableFonts = [
            'Inter',
            'Roboto',
            'Montserrat',
            'Open Sans',
            'Lato',
            'Poppins',
            'Arial',
            'Helvetica',
            'Times New Roman',
            'Georgia',
            'Verdana'
        ];
        
        // Text presets
        this.textPresets = {
            heading: {
                fontSize: 48,
                fontWeight: 'bold',
                fontFamily: 'Montserrat'
            },
            subheading: {
                fontSize: 32,
                fontWeight: '600',
                fontFamily: 'Inter'
            },
            body: {
                fontSize: 24,
                fontWeight: 'normal',
                fontFamily: 'Inter'
            },
            caption: {
                fontSize: 16,
                fontWeight: 'normal',
                fontFamily: 'Inter'
            }
        };
        
        this.init();
    }
    
    /**
     * Initialize text manager
     */
    init() {
        this.setupEventListeners();
        this.loadFonts();
        this.setupTextEditingBehavior();
        
        console.log('MobileTextManager initialized');
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Canvas text events
        this.canvas.on('text:editing:entered', (e) => {
            this.handleTextEditingEntered(e.target);
        });
        
        this.canvas.on('text:editing:exited', (e) => {
            this.handleTextEditingExited(e.target);
        });
        
        this.canvas.on('text:changed', (e) => {
            this.handleTextChanged(e.target);
        });
        
        // Object selection events
        this.canvas.on('selection:created', (e) => {
            this.handleSelection(e.selected);
        });
        
        this.canvas.on('selection:updated', (e) => {
            this.handleSelection(e.selected);
        });
        
        this.canvas.on('selection:cleared', () => {
            this.handleSelectionCleared();
        });
        
        // Mobile-specific touch events for text editing
        if (this.isMobile) {
            this.setupMobileTextEditing();
        }
    }
    
    /**
     * Setup mobile-specific text editing behavior
     */
    setupMobileTextEditing() {
        this.canvas.on('mouse:down', (e) => {
            if (e.target && e.target.type === 'text') {
                this.handleTextTouchStart(e.target, e.e);
            }
        });
        
        this.canvas.on('mouse:dblclick', (e) => {
            if (e.target && e.target.type === 'text') {
                this.enterTextEditing(e.target);
            }
        });
    }
    
    /**
     * Handle text touch start for mobile editing
     */
    handleTextTouchStart(textObj, event) {
        const currentTime = Date.now();
        const timeDiff = currentTime - this.lastTapTime;
        
        if (timeDiff < this.doubleTapThreshold) {
            // Double tap detected - enter editing mode
            this.enterTextEditing(textObj);
        } else {
            // Single tap - select text
            this.canvas.setActiveObject(textObj);
            this.canvas.renderAll();
        }
        
        this.lastTapTime = currentTime;
    }
    
    /**
     * Create new text object
     */
    createText(text = 'Your Text Here', options = {}) {
        const defaultOptions = {
            left: this.canvas.width / 2,
            top: this.canvas.height / 2,
            fontFamily: 'Inter',
            fontSize: 24,
            fill: '#000000',
            textAlign: 'center',
            originX: 'center',
            originY: 'center',
            editable: true,
            selectable: true,
            moveable: true,
            hasControls: true,
            hasBorders: true,
            lockUniScaling: false,
            splitByGrapheme: true // Better text editing on mobile
        };
        
        const textOptions = { ...defaultOptions, ...options };
        
        // Mobile-specific adjustments
        if (this.isMobile) {
            textOptions.cornerSize = 16;
            textOptions.borderScaleFactor = 2;
            textOptions.rotatingPointOffset = 30;
            textOptions.editingBorderColor = '#2563eb';
        }
        
        const textObject = new fabric.Text(text, textOptions);
        
        // Add custom properties
        textObject.set({
            id: this.generateTextId(),
            type: 'text',
            createdAt: Date.now(),
            isCustomText: true
        });
        
        // Add to canvas
        this.canvas.add(textObject);
        this.canvas.setActiveObject(textObject);
        this.canvas.renderAll();
        
        // Auto-enter editing mode for new text
        setTimeout(() => {
            this.enterTextEditing(textObject);
        }, 100);
        
        this.emit('text:created', { textObject });
        return textObject;
    }
    
    /**
     * Enter text editing mode
     */
    enterTextEditing(textObj) {
        if (!textObj || textObj.type !== 'text') return;
        
        this.currentTextObject = textObj;
        this.isEditing = true;
        
        // Enter editing mode
        textObj.enterEditing();
        textObj.selectAll();
        
        // Mobile-specific adjustments
        if (this.isMobile) {
            this.adjustViewportForEditing(textObj);
        }
        
        this.emit('text:editing:started', { textObject: textObj });
    }
    
    /**
     * Exit text editing mode
     */
    exitTextEditing() {
        if (this.currentTextObject) {
            this.currentTextObject.exitEditing();
            this.currentTextObject = null;
        }
        
        this.isEditing = false;
        this.canvas.renderAll();
        
        this.emit('text:editing:ended');
    }
    
    /**
     * Handle text editing entered
     */
    handleTextEditingEntered(textObj) {
        this.currentTextObject = textObj;
        this.isEditing = true;
        
        // Add editing class for styling
        this.canvas.upperCanvasEl.classList.add('text-editing');
        
        this.emit('text:editing:entered', { textObject: textObj });
    }
    
    /**
     * Handle text editing exited
     */
    handleTextEditingExited(textObj) {
        this.currentTextObject = null;
        this.isEditing = false;
        
        // Remove editing class
        this.canvas.upperCanvasEl.classList.remove('text-editing');
        
        // Save state
        this.canvasManager.saveState();
        
        this.emit('text:editing:exited', { textObject: textObj });
    }
    
    /**
     * Handle text changed
     */
    handleTextChanged(textObj) {
        // Auto-resize text if needed
        this.autoResizeText(textObj);
        
        this.emit('text:changed', { textObject: textObj });
    }
    
    /**
     * Handle selection
     */
    handleSelection(objects) {
        const textObjects = objects.filter(obj => obj.type === 'text');
        
        if (textObjects.length > 0) {
            this.emit('text:selected', { textObjects });
        }
    }
    
    /**
     * Handle selection cleared
     */
    handleSelectionCleared() {
        this.emit('text:deselected');
    }
    
    /**
     * Update text property for selected objects
     */
    updateTextProperty(property, value) {
        const activeObjects = this.canvas.getActiveObjects();
        const textObjects = activeObjects.filter(obj => obj.type === 'text');
        
        textObjects.forEach(obj => {
            obj.set(property, value);
        });
        
        this.canvas.renderAll();
        this.emit('text:property:updated', { property, value, objects: textObjects });
    }
    
    /**
     * Apply text preset
     */
    applyTextPreset(presetName) {
        const preset = this.textPresets[presetName];
        if (!preset) return;
        
        const activeObjects = this.canvas.getActiveObjects();
        const textObjects = activeObjects.filter(obj => obj.type === 'text');
        
        textObjects.forEach(obj => {
            obj.set(preset);
        });
        
        this.canvas.renderAll();
        this.emit('text:preset:applied', { preset: presetName, objects: textObjects });
    }
    
    /**
     * Set text font family
     */
    setFontFamily(fontFamily) {
        this.updateTextProperty('fontFamily', fontFamily);
    }
    
    /**
     * Set text font size
     */
    setFontSize(fontSize) {
        fontSize = Math.max(8, Math.min(200, parseInt(fontSize)));
        this.updateTextProperty('fontSize', fontSize);
    }
    
    /**
     * Set text color
     */
    setTextColor(color) {
        this.updateTextProperty('fill', color);
    }
    
    /**
     * Set text alignment
     */
    setTextAlignment(alignment) {
        this.updateTextProperty('textAlign', alignment);
    }
    
    /**
     * Toggle text bold
     */
    toggleBold() {
        const activeObjects = this.canvas.getActiveObjects();
        const textObjects = activeObjects.filter(obj => obj.type === 'text');
        
        textObjects.forEach(obj => {
            const currentWeight = obj.fontWeight;
            const newWeight = currentWeight === 'bold' ? 'normal' : 'bold';
            obj.set('fontWeight', newWeight);
        });
        
        this.canvas.renderAll();
        this.emit('text:bold:toggled', { objects: textObjects });
    }
    
    /**
     * Toggle text italic
     */
    toggleItalic() {
        const activeObjects = this.canvas.getActiveObjects();
        const textObjects = activeObjects.filter(obj => obj.type === 'text');
        
        textObjects.forEach(obj => {
            const currentStyle = obj.fontStyle;
            const newStyle = currentStyle === 'italic' ? 'normal' : 'italic';
            obj.set('fontStyle', newStyle);
        });
        
        this.canvas.renderAll();
        this.emit('text:italic:toggled', { objects: textObjects });
    }
    
    /**
     * Set text shadow
     */
    setTextShadow(shadowOptions) {
        const shadow = new fabric.Shadow({
            color: shadowOptions.color || '#000000',
            blur: shadowOptions.blur || 0,
            offsetX: shadowOptions.offsetX || 0,
            offsetY: shadowOptions.offsetY || 0,
            opacity: shadowOptions.opacity || 1
        });
        
        this.updateTextProperty('shadow', shadow);
    }
    
    /**
     * Remove text shadow
     */
    removeTextShadow() {
        this.updateTextProperty('shadow', null);
    }
    
    /**
     * Auto-resize text to fit content
     */
    autoResizeText(textObj) {
        if (!textObj || textObj.type !== 'text') return;
        
        // Get text dimensions
        const textWidth = textObj.width;
        const textHeight = textObj.height;
        
        // Check if text exceeds canvas bounds
        const canvasWidth = this.canvas.width;
        const canvasHeight = this.canvas.height;
        
        if (textWidth > canvasWidth * 0.9) {
            // Reduce font size to fit width
            const scaleFactor = (canvasWidth * 0.9) / textWidth;
            const newFontSize = Math.max(8, textObj.fontSize * scaleFactor);
            textObj.set('fontSize', newFontSize);
        }
        
        if (textHeight > canvasHeight * 0.9) {
            // Reduce font size to fit height
            const scaleFactor = (canvasHeight * 0.9) / textHeight;
            const newFontSize = Math.max(8, textObj.fontSize * scaleFactor);
            textObj.set('fontSize', newFontSize);
        }
    }
    
    /**
     * Adjust viewport for text editing on mobile
     */
    adjustViewportForEditing(textObj) {
        if (!this.isMobile) return;
        
        // Get text position
        const textBounds = textObj.getBoundingRect();
        const canvasElement = this.canvas.upperCanvasEl;
        const canvasRect = canvasElement.getBoundingClientRect();
        
        // Check if text is in viewport
        const textTop = canvasRect.top + textBounds.top;
        const viewportHeight = window.innerHeight;
        const keyboardHeight = viewportHeight * 0.4; // Estimated keyboard height
        
        if (textTop > viewportHeight - keyboardHeight) {
            // Scroll to bring text into view
            const scrollAmount = textTop - (viewportHeight - keyboardHeight - 100);
            window.scrollBy(0, scrollAmount);
        }
    }
    
    /**
     * Load web fonts
     */
    loadFonts() {
        // Load Google Fonts
        const fontFamilies = [
            'Inter:wght@400;500;600;700',
            'Roboto:wght@400;500;700',
            'Montserrat:wght@400;500;600;700',
            'Open+Sans:wght@400;600;700',
            'Lato:wght@400;700',
            'Poppins:wght@400;500;600;700'
        ];
        
        const link = document.createElement('link');
        link.href = `https://fonts.googleapis.com/css2?${fontFamilies.map(f => `family=${f}`).join('&')}&display=swap`;
        link.rel = 'stylesheet';
        document.head.appendChild(link);
        
        // Wait for fonts to load
        if (document.fonts) {
            document.fonts.ready.then(() => {
                this.emit('fonts:loaded');
            });
        }
    }
    
    /**
     * Setup text editing behavior
     */
    setupTextEditingBehavior() {
        // Prevent canvas deselection when clicking on text input
        this.canvas.on('before:selection:cleared', (e) => {
            if (this.isEditing) {
                e.e && e.e.preventDefault();
                return false;
            }
        });
        
        // Handle escape key to exit editing
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isEditing) {
                this.exitTextEditing();
            }
        });
    }
    
    /**
     * Get text properties of selected object
     */
    getSelectedTextProperties() {
        const activeObject = this.canvas.getActiveObject();
        
        if (!activeObject || activeObject.type !== 'text') {
            return null;
        }
        
        return {
            text: activeObject.text,
            fontFamily: activeObject.fontFamily,
            fontSize: activeObject.fontSize,
            fontWeight: activeObject.fontWeight,
            fontStyle: activeObject.fontStyle,
            fill: activeObject.fill,
            textAlign: activeObject.textAlign,
            shadow: activeObject.shadow,
            left: activeObject.left,
            top: activeObject.top,
            angle: activeObject.angle,
            scaleX: activeObject.scaleX,
            scaleY: activeObject.scaleY
        };
    }
    
    /**
     * Duplicate selected text
     */
    duplicateSelectedText() {
        const activeObject = this.canvas.getActiveObject();
        
        if (!activeObject || activeObject.type !== 'text') return;
        
        activeObject.clone((cloned) => {
            cloned.set({
                left: cloned.left + 20,
                top: cloned.top + 20,
                id: this.generateTextId()
            });
            
            this.canvas.add(cloned);
            this.canvas.setActiveObject(cloned);
            this.canvas.renderAll();
            
            this.emit('text:duplicated', { original: activeObject, duplicate: cloned });
        });
    }
    
    /**
     * Delete selected text
     */
    deleteSelectedText() {
        const activeObjects = this.canvas.getActiveObjects();
        const textObjects = activeObjects.filter(obj => obj.type === 'text');
        
        if (textObjects.length > 0) {
            textObjects.forEach(obj => {
                this.canvas.remove(obj);
            });
            
            this.canvas.discardActiveObject();
            this.canvas.renderAll();
            
            this.emit('text:deleted', { objects: textObjects });
        }
    }
    
    /**
     * Generate unique text ID
     */
    generateTextId() {
        return `text_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * Detect mobile device
     */
    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (navigator.maxTouchPoints && navigator.maxTouchPoints > 2);
    }
    
    /**
     * Event emitter
     */
    emit(eventName, data = {}) {
        const event = new CustomEvent(`text:${eventName}`, {
            detail: { ...data, textManager: this }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * Event listener
     */
    on(eventName, handler) {
        document.addEventListener(`text:${eventName}`, handler);
    }
    
    /**
     * Remove event listener
     */
    off(eventName, handler) {
        document.removeEventListener(`text:${eventName}`, handler);
    }
}

// Export for global use
window.MobileTextManager = MobileTextManager;
