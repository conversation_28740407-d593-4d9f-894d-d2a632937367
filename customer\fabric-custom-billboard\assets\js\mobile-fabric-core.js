/* ========================================
   MOBILE-FIRST FABRIC.JS CORE
   ======================================== */

/**
 * MobileFabricCore - Core class for mobile-first Fabric.js canvas editor
 * Handles canvas initialization, responsive scaling, and touch events
 */
class MobileFabricCore {
    constructor(canvasId, options = {}) {
        this.canvasId = canvasId;
        this.options = {
            width: 800,
            height: 400,
            backgroundColor: '#ffffff',
            selection: true,
            preserveObjectStacking: true,
            imageSmoothingEnabled: true,
            allowTouchScrolling: false,
            ...options
        };
        
        // Core properties
        this.canvas = null;
        this.canvasContainer = null;
        this.isInitialized = false;
        this.isMobile = this.detectMobile();
        this.currentZoom = 1;
        this.minZoom = 0.1;
        this.maxZoom = 5;
        
        // Touch handling
        this.touchData = {
            lastTouchTime: 0,
            touchStartDistance: 0,
            initialZoom: 1,
            isPinching: false,
            lastPanPoint: null
        };
        
        // Responsive scaling
        this.scalingData = {
            containerWidth: 0,
            containerHeight: 0,
            scaleRatio: 1,
            offsetX: 0,
            offsetY: 0
        };
        
        // Event listeners storage
        this.eventListeners = new Map();
        
        // Initialize
        this.init();
    }
    
    /**
     * Initialize the Fabric.js canvas with mobile-first settings
     */
    init() {
        try {
            console.log('🔄 Starting MobileFabricCore initialization...');

            // Check if Fabric.js is available
            if (typeof fabric === 'undefined') {
                throw new Error('Fabric.js is not loaded');
            }
            console.log('✅ Fabric.js is available');

            // Find canvas container
            this.canvasContainer = document.getElementById('canvasContainer');
            if (!this.canvasContainer) {
                console.error('❌ Canvas container element not found. Looking for element with ID: canvasContainer');
                throw new Error('Canvas container not found');
            }
            console.log('✅ Canvas container found');

            // Find canvas element
            const canvasElement = document.getElementById(this.canvasId);
            if (!canvasElement) {
                console.error(`❌ Canvas element not found. Looking for element with ID: ${this.canvasId}`);
                throw new Error(`Canvas element with ID '${this.canvasId}' not found`);
            }
            console.log('✅ Canvas element found');

            // Create Fabric.js canvas
            console.log('🔄 Creating Fabric.js canvas...');
            this.canvas = new fabric.Canvas(this.canvasId, {
                width: this.options.width,
                height: this.options.height,
                backgroundColor: this.options.backgroundColor,
                selection: this.options.selection,
                preserveObjectStacking: this.options.preserveObjectStacking,
                imageSmoothingEnabled: this.options.imageSmoothingEnabled,
                allowTouchScrolling: this.options.allowTouchScrolling,
                enableRetinaScaling: true,
                devicePixelRatio: window.devicePixelRatio || 1
            });

            if (!this.canvas) {
                throw new Error('Failed to create Fabric.js canvas instance');
            }
            console.log('✅ Fabric.js canvas created');

            // Configure mobile-specific settings
            console.log('🔄 Configuring mobile settings...');
            this.configureMobileSettings();
            console.log('✅ Mobile settings configured');

            // Setup responsive scaling
            console.log('🔄 Setting up responsive scaling...');
            this.setupResponsiveScaling();
            console.log('✅ Responsive scaling setup complete');

            // Setup touch events
            console.log('🔄 Setting up touch events...');
            this.setupTouchEvents();
            console.log('✅ Touch events setup complete');

            // Setup canvas events
            console.log('🔄 Setting up canvas events...');
            this.setupCanvasEvents();
            console.log('✅ Canvas events setup complete');

            // Initial resize
            console.log('🔄 Performing initial resize...');
            this.handleResize();
            console.log('✅ Initial resize complete');

            this.isInitialized = true;

            // Emit initialization event with delay to ensure listeners are ready
            setTimeout(() => {
                this.emit('initialized', { canvas: this.canvas });
                console.log('✅ MobileFabricCore initialized successfully');
            }, 10);

        } catch (error) {
            console.error('❌ Failed to initialize MobileFabricCore:', error);
            console.error('Error details:', {
                message: error.message,
                stack: error.stack,
                canvasId: this.canvasId,
                containerExists: !!document.getElementById('canvasContainer'),
                canvasExists: !!document.getElementById(this.canvasId),
                fabricAvailable: typeof fabric !== 'undefined'
            });

            // Emit error event with delay
            setTimeout(() => {
                this.emit('error', { error });
            }, 10);
        }
    }
    
    /**
     * Configure mobile-specific Fabric.js settings
     */
    configureMobileSettings() {
        if (this.isMobile) {
            // Mobile-specific configurations
            this.canvas.selection = true;
            this.canvas.skipTargetFind = false;
            this.canvas.targetFindTolerance = 10; // Larger touch targets
            
            // Disable default touch scrolling
            this.canvas.allowTouchScrolling = false;
            
            // Configure object controls for touch
            fabric.Object.prototype.set({
                borderColor: '#2563eb',
                borderScaleFactor: 2,
                cornerColor: '#ffffff',
                cornerStrokeColor: '#2563eb',
                cornerStyle: 'circle',
                cornerSize: 16,
                transparentCorners: false,
                rotatingPointOffset: 30
            });
        } else {
            // Desktop configurations
            fabric.Object.prototype.set({
                borderColor: '#2563eb',
                borderScaleFactor: 1,
                cornerColor: '#ffffff',
                cornerStrokeColor: '#2563eb',
                cornerStyle: 'rect',
                cornerSize: 12,
                transparentCorners: false,
                rotatingPointOffset: 20
            });
        }
    }
    
    /**
     * Setup responsive canvas scaling
     */
    setupResponsiveScaling() {
        // Add resize listener
        this.addEventListener(window, 'resize', this.debounce(() => {
            this.handleResize();
        }, 250));
        
        // Add orientation change listener for mobile
        if (this.isMobile) {
            this.addEventListener(window, 'orientationchange', () => {
                setTimeout(() => this.handleResize(), 100);
            });
        }
    }
    
    /**
     * Handle canvas resize and scaling
     */
    handleResize() {
        if (!this.canvas || !this.canvasContainer) return;
        
        const containerRect = this.canvasContainer.getBoundingClientRect();
        const containerWidth = containerRect.width;
        const containerHeight = containerRect.height;
        
        // Calculate scale ratio to fit canvas in container
        const scaleX = containerWidth / this.options.width;
        const scaleY = containerHeight / this.options.height;
        const scaleRatio = Math.min(scaleX, scaleY, 1); // Don't scale up beyond 100%
        
        // Update scaling data
        this.scalingData = {
            containerWidth,
            containerHeight,
            scaleRatio,
            offsetX: (containerWidth - (this.options.width * scaleRatio)) / 2,
            offsetY: (containerHeight - (this.options.height * scaleRatio)) / 2
        };
        
        // Apply scaling to canvas
        this.canvas.setDimensions({
            width: this.options.width * scaleRatio,
            height: this.options.height * scaleRatio
        });
        
        // Set zoom to maintain aspect ratio
        this.canvas.setZoom(scaleRatio);
        
        // Center the canvas
        this.canvas.absolutePan({
            x: -this.scalingData.offsetX / scaleRatio,
            y: -this.scalingData.offsetY / scaleRatio
        });
        
        this.currentZoom = scaleRatio;
        this.canvas.renderAll();
        
        this.emit('resized', {
            containerWidth,
            containerHeight,
            scaleRatio,
            zoom: this.currentZoom
        });
    }
    
    /**
     * Setup touch event handlers for mobile devices
     */
    setupTouchEvents() {
        if (!this.isMobile) return;
        
        const canvasElement = this.canvas.upperCanvasEl;
        
        // Touch start
        this.addEventListener(canvasElement, 'touchstart', (e) => {
            this.handleTouchStart(e);
        }, { passive: false });
        
        // Touch move
        this.addEventListener(canvasElement, 'touchmove', (e) => {
            this.handleTouchMove(e);
        }, { passive: false });
        
        // Touch end
        this.addEventListener(canvasElement, 'touchend', (e) => {
            this.handleTouchEnd(e);
        }, { passive: false });
        
        // Prevent default touch behaviors
        this.addEventListener(canvasElement, 'touchstart', (e) => {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        }, { passive: false });
    }
    
    /**
     * Handle touch start events
     */
    handleTouchStart(e) {
        const touches = e.touches;
        
        if (touches.length === 2) {
            // Two finger touch - prepare for pinch zoom
            this.touchData.isPinching = true;
            this.touchData.touchStartDistance = this.getTouchDistance(touches[0], touches[1]);
            this.touchData.initialZoom = this.currentZoom;
            e.preventDefault();
        } else if (touches.length === 1) {
            // Single touch
            this.touchData.lastPanPoint = {
                x: touches[0].clientX,
                y: touches[0].clientY
            };
        }
        
        this.touchData.lastTouchTime = Date.now();
    }
    
    /**
     * Handle touch move events
     */
    handleTouchMove(e) {
        const touches = e.touches;
        
        if (touches.length === 2 && this.touchData.isPinching) {
            // Pinch zoom
            e.preventDefault();
            
            const currentDistance = this.getTouchDistance(touches[0], touches[1]);
            const scale = currentDistance / this.touchData.touchStartDistance;
            const newZoom = this.touchData.initialZoom * scale;
            
            this.setZoom(newZoom);
            
        } else if (touches.length === 1 && this.touchData.lastPanPoint && !this.canvas.getActiveObject()) {
            // Pan canvas (only if no object is selected)
            e.preventDefault();
            
            const deltaX = touches[0].clientX - this.touchData.lastPanPoint.x;
            const deltaY = touches[0].clientY - this.touchData.lastPanPoint.y;
            
            this.panCanvas(deltaX, deltaY);
            
            this.touchData.lastPanPoint = {
                x: touches[0].clientX,
                y: touches[0].clientY
            };
        }
    }
    
    /**
     * Handle touch end events
     */
    handleTouchEnd(e) {
        if (e.touches.length === 0) {
            // All touches ended
            this.touchData.isPinching = false;
            this.touchData.lastPanPoint = null;
        } else if (e.touches.length === 1) {
            // One touch remaining
            this.touchData.isPinching = false;
            this.touchData.lastPanPoint = {
                x: e.touches[0].clientX,
                y: e.touches[0].clientY
            };
        }
    }
    
    /**
     * Setup canvas event handlers
     */
    setupCanvasEvents() {
        // Object selection
        this.canvas.on('selection:created', (e) => {
            this.emit('object:selected', { objects: e.selected });
        });
        
        this.canvas.on('selection:updated', (e) => {
            this.emit('object:selected', { objects: e.selected });
        });
        
        this.canvas.on('selection:cleared', () => {
            this.emit('object:deselected');
        });
        
        // Object modification
        this.canvas.on('object:modified', (e) => {
            this.emit('object:modified', { target: e.target });
        });
        
        // Object movement
        this.canvas.on('object:moving', (e) => {
            this.emit('object:moving', { target: e.target });
        });
        
        // Object scaling
        this.canvas.on('object:scaling', (e) => {
            this.emit('object:scaling', { target: e.target });
        });
        
        // Object rotation
        this.canvas.on('object:rotating', (e) => {
            this.emit('object:rotating', { target: e.target });
        });
        
        // Canvas events
        this.canvas.on('canvas:cleared', () => {
            this.emit('canvas:cleared');
        });
        
        // Path events for drawing
        this.canvas.on('path:created', (e) => {
            this.emit('path:created', { path: e.path });
        });
    }
    
    /**
     * Set canvas zoom level
     */
    setZoom(zoom, point = null) {
        zoom = Math.max(this.minZoom, Math.min(this.maxZoom, zoom));
        
        if (point) {
            this.canvas.zoomToPoint(point, zoom);
        } else {
            this.canvas.setZoom(zoom);
        }
        
        this.currentZoom = zoom;
        this.emit('zoom:changed', { zoom });
    }
    
    /**
     * Pan the canvas
     */
    panCanvas(deltaX, deltaY) {
        const vpt = this.canvas.viewportTransform;
        vpt[4] += deltaX;
        vpt[5] += deltaY;
        this.canvas.setViewportTransform(vpt);
        this.canvas.renderAll();
    }
    
    /**
     * Fit canvas to viewport
     */
    fitToViewport() {
        this.handleResize();
        this.emit('viewport:fitted');
    }
    
    /**
     * Reset zoom to 100%
     */
    resetZoom() {
        this.setZoom(this.scalingData.scaleRatio);
        this.canvas.absolutePan({ x: 0, y: 0 });
        this.emit('zoom:reset');
    }
    
    /**
     * Get touch distance between two points
     */
    getTouchDistance(touch1, touch2) {
        const dx = touch1.clientX - touch2.clientX;
        const dy = touch1.clientY - touch2.clientY;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    /**
     * Detect if device is mobile
     */
    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (navigator.maxTouchPoints && navigator.maxTouchPoints > 2);
    }
    
    /**
     * Debounce function for performance
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * Add event listener with cleanup tracking
     */
    addEventListener(element, event, handler, options = {}) {
        element.addEventListener(event, handler, options);
        
        if (!this.eventListeners.has(element)) {
            this.eventListeners.set(element, []);
        }
        this.eventListeners.get(element).push({ event, handler, options });
    }
    
    /**
     * Event emitter functionality
     */
    emit(eventName, data = {}) {
        const event = new CustomEvent(`fabric:${eventName}`, {
            detail: { ...data, canvas: this.canvas, core: this }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * Listen to events
     */
    on(eventName, handler) {
        document.addEventListener(`fabric:${eventName}`, handler);
    }
    
    /**
     * Remove event listener
     */
    off(eventName, handler) {
        document.removeEventListener(`fabric:${eventName}`, handler);
    }
    
    /**
     * Cleanup and destroy
     */
    destroy() {
        // Remove all event listeners
        this.eventListeners.forEach((listeners, element) => {
            listeners.forEach(({ event, handler, options }) => {
                element.removeEventListener(event, handler, options);
            });
        });
        this.eventListeners.clear();
        
        // Dispose canvas
        if (this.canvas) {
            this.canvas.dispose();
            this.canvas = null;
        }
        
        this.isInitialized = false;
        this.emit('destroyed');
    }
    
    /**
     * Get canvas instance
     */
    getCanvas() {
        return this.canvas;
    }
    
    /**
     * Get current zoom level
     */
    getZoom() {
        return this.currentZoom;
    }
    
    /**
     * Check if mobile device
     */
    isMobileDevice() {
        return this.isMobile;
    }
    
    /**
     * Get scaling data
     */
    getScalingData() {
        return { ...this.scalingData };
    }
}

// Export for global use
window.MobileFabricCore = MobileFabricCore;
