/* ========================================
   MOBILE BACKGROUND MANAGER
   ======================================== */

/**
 * MobileBackgroundManager - Manages background template modal and selection
 * Handles category navigation, template selection, and mobile-optimized UI
 */
class MobileBackgroundManager {
    constructor(canvasManager) {
        this.canvasManager = canvasManager;
        this.canvas = canvasManager.canvas;
        
        // Modal elements
        this.modal = document.getElementById('backgroundModal');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalContent = document.getElementById('modalContent');
        this.categoryStep = document.getElementById('categoryStep');
        this.templateStep = document.getElementById('templateStep');
        this.categoryGrid = document.getElementById('categoryGrid');
        this.templateGrid = document.getElementById('templateGrid');
        
        // Modal controls
        this.modalClose = document.getElementById('modalClose');
        this.modalBack = document.getElementById('modalBack');
        this.modalCancel = document.getElementById('modalCancel');
        this.modalApply = document.getElementById('modalApply');
        
        // State management
        this.isOpen = false;
        this.currentStep = 'category';
        this.selectedCategory = null;
        this.selectedTemplate = null;
        this.templates = {};
        
        // Mobile optimization
        this.isMobile = this.detectMobile();
        this.touchStartY = 0;
        this.touchEndY = 0;
        
        this.init();
    }
    
    /**
     * Initialize background manager
     */
    init() {
        this.loadTemplateData();
        this.setupEventListeners();
        this.setupModalBehavior();
        this.renderCategories();
        
        console.log('MobileBackgroundManager initialized');
    }
    
    /**
     * Load template data from background-template-data.js
     */
    loadTemplateData() {
        // Check if global template data is available
        if (typeof window.BACKGROUND_TEMPLATES !== 'undefined') {
            this.templates = window.BACKGROUND_TEMPLATES;
            console.log('✅ Background templates loaded from global data');
        } else {
            // Fallback template data
            console.warn('⚠️ Using fallback template data - background-template-data.js may not have loaded');
            this.templates = {
                'Anniversary': [
                    { id: 'anniversary1', url: '../../../../stock-image/Anniversary/Anniversary-1.png', name: 'Anniversary Image 1' }
                ],
                'Love': [
                    { id: 'love1', url: '../../../../stock-image/Love/Love-1.png', name: 'Love Image 1' }
                ],
                'Wedding': [
                    { id: 'wedding1', url: '../../../../stock-image/Wedding/Wedding-1.png', name: 'Wedding Image 1' }
                ]
            };
        }

        this.emit('templates:loaded', { templates: this.templates });
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Modal controls
        if (this.modalClose) {
            this.modalClose.addEventListener('click', () => this.closeModal());
        }
        
        if (this.modalBack) {
            this.modalBack.addEventListener('click', () => this.goBack());
        }
        
        if (this.modalCancel) {
            this.modalCancel.addEventListener('click', () => this.closeModal());
        }
        
        if (this.modalApply) {
            this.modalApply.addEventListener('click', () => this.applySelectedTemplate());
        }
        
        // Modal overlay click to close
        if (this.modal) {
            this.modal.addEventListener('click', (e) => {
                if (e.target === this.modal) {
                    this.closeModal();
                }
            });
        }
        
        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeModal();
            }
        });
        
        // Listen for toolbar events
        document.addEventListener('toolbar:background:choose', () => {
            this.openModal();
        });
        
        // Mobile touch events for swipe gestures
        if (this.isMobile && this.modal) {
            this.setupMobileGestures();
        }
    }
    
    /**
     * Setup mobile gesture handling
     */
    setupMobileGestures() {
        this.modal.addEventListener('touchstart', (e) => {
            this.touchStartY = e.touches[0].clientY;
        }, { passive: true });
        
        this.modal.addEventListener('touchend', (e) => {
            this.touchEndY = e.changedTouches[0].clientY;
            this.handleSwipeGesture();
        }, { passive: true });
    }
    
    /**
     * Handle swipe gestures
     */
    handleSwipeGesture() {
        const swipeDistance = this.touchStartY - this.touchEndY;
        const minSwipeDistance = 100;
        
        if (Math.abs(swipeDistance) > minSwipeDistance) {
            if (swipeDistance > 0) {
                // Swipe up - could be used for additional actions
            } else {
                // Swipe down - close modal
                this.closeModal();
            }
        }
    }
    
    /**
     * Setup modal behavior
     */
    setupModalBehavior() {
        // Prevent body scroll when modal is open
        this.modal?.addEventListener('wheel', (e) => {
            e.preventDefault();
        }, { passive: false });
        
        // Focus management for accessibility
        this.modal?.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                this.handleTabNavigation(e);
            }
        });
    }
    
    /**
     * Open modal
     */
    openModal() {
        if (!this.modal) return;
        
        this.isOpen = true;
        this.currentStep = 'category';
        this.selectedCategory = null;
        this.selectedTemplate = null;
        
        // Show modal
        this.modal.style.display = 'flex';
        setTimeout(() => {
            this.modal.classList.add('show');
        }, 10);
        
        // Update UI
        this.updateModalState();
        this.renderCategories();
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
        
        // Focus management
        this.modalClose?.focus();
        
        this.emit('modal:opened');
    }
    
    /**
     * Close modal
     */
    closeModal() {
        if (!this.modal) return;
        
        this.modal.classList.remove('show');
        
        setTimeout(() => {
            this.modal.style.display = 'none';
            this.isOpen = false;
            
            // Restore body scroll
            document.body.style.overflow = '';
            
            this.emit('modal:closed');
        }, 300);
    }
    
    /**
     * Go back to previous step
     */
    goBack() {
        if (this.currentStep === 'template') {
            this.currentStep = 'category';
            this.selectedTemplate = null;
            this.updateModalState();
            this.renderCategories();
        }
    }
    
    /**
     * Update modal state and UI
     */
    updateModalState() {
        if (!this.modalTitle || !this.modalBack || !this.modalApply) return;
        
        switch (this.currentStep) {
            case 'category':
                this.modalTitle.textContent = 'Choose Background Category';
                this.modalBack.style.display = 'none';
                this.modalApply.disabled = true;
                this.categoryStep.style.display = 'block';
                this.templateStep.style.display = 'none';
                break;
                
            case 'template':
                this.modalTitle.textContent = `${this.selectedCategory} Templates`;
                this.modalBack.style.display = 'flex';
                this.modalApply.disabled = !this.selectedTemplate;
                this.categoryStep.style.display = 'none';
                this.templateStep.style.display = 'block';
                break;
        }
    }
    
    /**
     * Render category grid
     */
    renderCategories() {
        if (!this.categoryGrid) return;
        
        this.categoryGrid.innerHTML = '';
        
        const categories = Object.keys(this.templates);
        
        categories.forEach(category => {
            const categoryItem = this.createCategoryItem(category);
            this.categoryGrid.appendChild(categoryItem);
        });
    }
    
    /**
     * Create category item element
     */
    createCategoryItem(category) {
        const item = document.createElement('div');
        item.className = 'category-item';
        item.setAttribute('data-category', category);
        item.setAttribute('tabindex', '0');
        item.setAttribute('role', 'button');
        item.setAttribute('aria-label', `Select ${category} category`);
        
        // Category icon (you can customize these)
        const iconMap = {
            'Anniversary': 'fas fa-heart',
            'Benefit': 'fas fa-gift',
            'Christian': 'fas fa-cross',
            'Graduation': 'fas fa-graduation-cap',
            'Holiday': 'fas fa-calendar-alt',
            'Local School': 'fas fa-school',
            'Love': 'fas fa-heart',
            'Marry Me': 'fas fa-ring',
            'New Born': 'fas fa-baby',
            'Obituary': 'fas fa-dove',
            'Other': 'fas fa-image',
            'Pet': 'fas fa-paw',
            'Prayer': 'fas fa-praying-hands',
            'Retirement': 'fas fa-rocking-chair',
            'Wedding': 'fas fa-rings-wedding',
            'Welcome': 'fas fa-hand-wave'
        };
        
        const icon = iconMap[category] || 'fas fa-image';
        
        item.innerHTML = `
            <i class="${icon}"></i>
            <span class="category-name">${category}</span>
        `;
        
        // Event listeners
        item.addEventListener('click', () => this.selectCategory(category));
        item.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.selectCategory(category);
            }
        });
        
        return item;
    }
    
    /**
     * Select category and show templates
     */
    selectCategory(category) {
        this.selectedCategory = category;
        this.currentStep = 'template';
        this.updateModalState();
        this.renderTemplates(category);
        
        this.emit('category:selected', { category });
    }
    
    /**
     * Render template grid for selected category
     */
    renderTemplates(category) {
        if (!this.templateGrid) return;
        
        this.templateGrid.innerHTML = '';
        
        const templates = this.templates[category] || [];
        
        if (templates.length === 0) {
            this.showEmptyState();
            return;
        }
        
        templates.forEach(template => {
            const templateItem = this.createTemplateItem(template);
            this.templateGrid.appendChild(templateItem);
        });
    }
    
    /**
     * Create template item element
     */
    createTemplateItem(template) {
        const item = document.createElement('div');
        item.className = 'template-item';
        item.setAttribute('data-template-id', template.id);
        item.setAttribute('tabindex', '0');
        item.setAttribute('role', 'button');
        item.setAttribute('aria-label', `Select ${template.name}`);
        
        item.innerHTML = `
            <img src="${template.url}" alt="${template.name}" class="template-image" loading="lazy">
            <div class="template-overlay">
                <span>${template.name}</span>
            </div>
        `;
        
        // Event listeners
        item.addEventListener('click', () => this.selectTemplate(template));
        item.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.selectTemplate(template);
            }
        });
        
        // Image error handling
        const img = item.querySelector('.template-image');
        img.addEventListener('error', () => {
            item.classList.add('image-error');
            img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk0YTNiOCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIE5vdCBGb3VuZDwvdGV4dD48L3N2Zz4=';
        });
        
        return item;
    }
    
    /**
     * Select template
     */
    selectTemplate(template) {
        // Remove previous selection
        this.templateGrid.querySelectorAll('.template-item').forEach(item => {
            item.classList.remove('selected');
        });
        
        // Add selection to current item
        const templateItem = this.templateGrid.querySelector(`[data-template-id="${template.id}"]`);
        if (templateItem) {
            templateItem.classList.add('selected');
        }
        
        this.selectedTemplate = template;
        
        // Enable apply button
        if (this.modalApply) {
            this.modalApply.disabled = false;
        }
        
        this.emit('template:selected', { template });
    }
    
    /**
     * Apply selected template
     */
    applySelectedTemplate() {
        if (!this.selectedTemplate) return;
        
        this.showLoading();
        
        // Apply background to canvas
        this.canvasManager.setBackgroundImage(this.selectedTemplate.url)
            .then(() => {
                this.hideLoading();
                this.closeModal();
                this.showToast('Background applied successfully');
                this.emit('template:applied', { template: this.selectedTemplate });
            })
            .catch((error) => {
                this.hideLoading();
                this.showToast('Failed to apply background', 'error');
                this.emit('template:error', { error, template: this.selectedTemplate });
            });
    }
    
    /**
     * Show empty state
     */
    showEmptyState() {
        this.templateGrid.innerHTML = `
            <div class="modal-empty">
                <i class="fas fa-image"></i>
                <p>No templates available in this category</p>
            </div>
        `;
    }
    
    /**
     * Show loading state
     */
    showLoading() {
        if (this.modalContent) {
            const loading = document.createElement('div');
            loading.className = 'modal-loading';
            loading.id = 'modalLoading';
            loading.innerHTML = `
                <div class="loading-spinner"></div>
                <p>Applying background...</p>
            `;
            this.modalContent.appendChild(loading);
        }
    }
    
    /**
     * Hide loading state
     */
    hideLoading() {
        const loading = document.getElementById('modalLoading');
        if (loading) {
            loading.remove();
        }
    }
    
    /**
     * Handle tab navigation for accessibility
     */
    handleTabNavigation(e) {
        const focusableElements = this.modal.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (e.shiftKey) {
            if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            }
        } else {
            if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    }
    
    /**
     * Show toast notification
     */
    showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        Object.assign(toast.style, {
            position: 'fixed',
            bottom: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: type === 'error' ? '#dc2626' : '#059669',
            color: 'white',
            padding: '12px 24px',
            borderRadius: '8px',
            zIndex: '10000',
            fontSize: '14px',
            fontWeight: '500',
            boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 100);
        
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }
    
    /**
     * Detect mobile device
     */
    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (navigator.maxTouchPoints && navigator.maxTouchPoints > 2);
    }
    
    /**
     * Get available categories
     */
    getCategories() {
        return Object.keys(this.templates);
    }
    
    /**
     * Get templates for category
     */
    getTemplatesForCategory(category) {
        return this.templates[category] || [];
    }
    
    /**
     * Event emitter
     */
    emit(eventName, data = {}) {
        const event = new CustomEvent(`background:${eventName}`, {
            detail: { ...data, backgroundManager: this }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * Event listener
     */
    on(eventName, handler) {
        document.addEventListener(`background:${eventName}`, handler);
    }
    
    /**
     * Remove event listener
     */
    off(eventName, handler) {
        document.removeEventListener(`background:${eventName}`, handler);
    }
}

// Export for global use
window.MobileBackgroundManager = MobileBackgroundManager;
