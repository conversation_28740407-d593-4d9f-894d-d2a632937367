/* ========================================
   MOBILE CANVAS MANAGER
   ======================================== */

/**
 * MobileCanvasManager - Manages canvas operations and object manipulation
 * Built on top of MobileFabricCore for mobile-first canvas editing
 */
class MobileCanvasManager {
    constructor(fabricCore) {
        this.core = fabricCore;
        this.canvas = fabricCore.getCanvas();
        
        // Object management
        this.objects = new Map();
        this.objectCounter = 0;
        this.selectedObjects = [];
        
        // Canvas state
        this.canvasHistory = [];
        this.historyIndex = -1;
        this.maxHistorySize = 50;
        
        // Background management
        this.backgroundImage = null;
        this.backgroundColor = '#ffffff';
        
        // Grid and snapping
        this.gridEnabled = false;
        this.snapToGrid = false;
        this.gridSize = 20;
        this.snapThreshold = 10;
        
        // Performance settings
        this.renderOnAddRemove = true;
        this.stateful = true;
        
        this.init();
    }
    
    /**
     * Initialize canvas manager
     */
    init() {
        this.setupEventListeners();
        this.saveState(); // Initial state
        console.log('MobileCanvasManager initialized');
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen to core events
        this.core.on('object:selected', (e) => {
            this.selectedObjects = e.detail.objects || [];
            this.emit('selection:changed', { objects: this.selectedObjects });
        });
        
        this.core.on('object:deselected', () => {
            this.selectedObjects = [];
            this.emit('selection:changed', { objects: [] });
        });
        
        this.core.on('object:modified', (e) => {
            this.saveState();
            this.emit('object:changed', { object: e.detail.target });
        });
        
        // Canvas events
        this.canvas.on('object:added', (e) => {
            if (e.target && !e.target.isBackground) {
                this.registerObject(e.target);
                if (this.renderOnAddRemove) {
                    this.saveState();
                }
            }
        });
        
        this.canvas.on('object:removed', (e) => {
            if (e.target && !e.target.isBackground) {
                this.unregisterObject(e.target);
                if (this.renderOnAddRemove) {
                    this.saveState();
                }
            }
        });
    }
    
    /**
     * Add text object to canvas
     */
    addText(text = 'Sample Text', options = {}) {
        const defaultOptions = {
            left: this.canvas.width / 2,
            top: this.canvas.height / 2,
            fontFamily: 'Inter',
            fontSize: 24,
            fill: '#000000',
            textAlign: 'center',
            originX: 'center',
            originY: 'center',
            editable: true,
            selectable: true,
            moveable: true,
            hasControls: true,
            hasBorders: true,
            lockUniScaling: false
        };
        
        const textOptions = { ...defaultOptions, ...options };
        
        const textObject = new fabric.Text(text, textOptions);
        
        // Add mobile-specific properties
        if (this.core.isMobileDevice()) {
            textObject.set({
                cornerSize: 16,
                borderScaleFactor: 2,
                rotatingPointOffset: 30
            });
        }
        
        // Add custom properties
        textObject.set({
            id: this.generateObjectId(),
            type: 'text',
            createdAt: Date.now()
        });
        
        this.canvas.add(textObject);
        this.canvas.setActiveObject(textObject);
        this.canvas.renderAll();
        
        this.emit('text:added', { object: textObject });
        return textObject;
    }
    
    /**
     * Add image object to canvas
     */
    addImage(imageUrl, options = {}) {
        return new Promise((resolve, reject) => {
            fabric.Image.fromURL(imageUrl, (img) => {
                if (!img) {
                    reject(new Error('Failed to load image'));
                    return;
                }
                
                const defaultOptions = {
                    left: this.canvas.width / 2,
                    top: this.canvas.height / 2,
                    originX: 'center',
                    originY: 'center',
                    selectable: true,
                    moveable: true,
                    hasControls: true,
                    hasBorders: true,
                    lockUniScaling: false
                };
                
                const imageOptions = { ...defaultOptions, ...options };
                
                // Scale image to fit canvas if too large
                const maxWidth = this.canvas.width * 0.8;
                const maxHeight = this.canvas.height * 0.8;
                
                if (img.width > maxWidth || img.height > maxHeight) {
                    const scaleX = maxWidth / img.width;
                    const scaleY = maxHeight / img.height;
                    const scale = Math.min(scaleX, scaleY);
                    
                    imageOptions.scaleX = scale;
                    imageOptions.scaleY = scale;
                }
                
                img.set(imageOptions);
                
                // Add mobile-specific properties
                if (this.core.isMobileDevice()) {
                    img.set({
                        cornerSize: 16,
                        borderScaleFactor: 2,
                        rotatingPointOffset: 30
                    });
                }
                
                // Add custom properties
                img.set({
                    id: this.generateObjectId(),
                    type: 'image',
                    createdAt: Date.now()
                });
                
                this.canvas.add(img);
                this.canvas.setActiveObject(img);
                this.canvas.renderAll();
                
                this.emit('image:added', { object: img });
                resolve(img);
                
            }, { crossOrigin: 'anonymous' });
        });
    }
    
    /**
     * Set canvas background image
     */
    setBackgroundImage(imageUrl) {
        return new Promise((resolve, reject) => {
            if (!imageUrl) {
                this.clearBackground();
                resolve();
                return;
            }
            
            fabric.Image.fromURL(imageUrl, (img) => {
                if (!img) {
                    reject(new Error('Failed to load background image'));
                    return;
                }
                
                // Scale image to cover canvas
                const scaleX = this.canvas.width / img.width;
                const scaleY = this.canvas.height / img.height;
                const scale = Math.max(scaleX, scaleY);
                
                img.set({
                    scaleX: scale,
                    scaleY: scale,
                    left: this.canvas.width / 2,
                    top: this.canvas.height / 2,
                    originX: 'center',
                    originY: 'center',
                    selectable: false,
                    evented: false,
                    isBackground: true
                });
                
                this.canvas.setBackgroundImage(img, () => {
                    this.backgroundImage = img;
                    this.canvas.renderAll();
                    this.saveState();
                    this.emit('background:changed', { image: img });
                    resolve(img);
                });
                
            }, { crossOrigin: 'anonymous' });
        });
    }
    
    /**
     * Set canvas background color
     */
    setBackgroundColor(color) {
        this.backgroundColor = color;
        this.canvas.setBackgroundColor(color, () => {
            this.canvas.renderAll();
            this.saveState();
            this.emit('background:changed', { color });
        });
    }
    
    /**
     * Clear canvas background
     */
    clearBackground() {
        this.backgroundImage = null;
        this.canvas.setBackgroundImage(null, () => {
            this.canvas.setBackgroundColor(this.backgroundColor, () => {
                this.canvas.renderAll();
                this.saveState();
                this.emit('background:cleared');
            });
        });
    }
    
    /**
     * Delete selected objects
     */
    deleteSelected() {
        const activeObjects = this.canvas.getActiveObjects();
        if (activeObjects.length > 0) {
            activeObjects.forEach(obj => {
                this.canvas.remove(obj);
            });
            this.canvas.discardActiveObject();
            this.canvas.renderAll();
            this.emit('objects:deleted', { objects: activeObjects });
        }
    }
    
    /**
     * Clear all objects from canvas
     */
    clearCanvas() {
        const objects = this.canvas.getObjects().filter(obj => !obj.isBackground);
        objects.forEach(obj => this.canvas.remove(obj));
        this.canvas.discardActiveObject();
        this.canvas.renderAll();
        this.saveState();
        this.emit('canvas:cleared');
    }
    
    /**
     * Duplicate selected objects
     */
    duplicateSelected() {
        const activeObjects = this.canvas.getActiveObjects();
        if (activeObjects.length > 0) {
            const duplicates = [];
            
            activeObjects.forEach(obj => {
                obj.clone((cloned) => {
                    cloned.set({
                        left: cloned.left + 20,
                        top: cloned.top + 20,
                        id: this.generateObjectId()
                    });
                    this.canvas.add(cloned);
                    duplicates.push(cloned);
                    
                    if (duplicates.length === activeObjects.length) {
                        this.canvas.renderAll();
                        this.emit('objects:duplicated', { objects: duplicates });
                    }
                });
            });
        }
    }
    
    /**
     * Bring selected objects to front
     */
    bringToFront() {
        const activeObjects = this.canvas.getActiveObjects();
        activeObjects.forEach(obj => {
            this.canvas.bringToFront(obj);
        });
        this.canvas.renderAll();
        this.saveState();
        this.emit('objects:layered', { action: 'front', objects: activeObjects });
    }
    
    /**
     * Send selected objects to back
     */
    sendToBack() {
        const activeObjects = this.canvas.getActiveObjects();
        activeObjects.forEach(obj => {
            this.canvas.sendToBack(obj);
        });
        this.canvas.renderAll();
        this.saveState();
        this.emit('objects:layered', { action: 'back', objects: activeObjects });
    }
    
    /**
     * Group selected objects
     */
    groupSelected() {
        const activeObjects = this.canvas.getActiveObjects();
        if (activeObjects.length > 1) {
            const group = new fabric.Group(activeObjects, {
                id: this.generateObjectId(),
                type: 'group',
                createdAt: Date.now()
            });
            
            this.canvas.remove(...activeObjects);
            this.canvas.add(group);
            this.canvas.setActiveObject(group);
            this.canvas.renderAll();
            this.emit('objects:grouped', { group, objects: activeObjects });
        }
    }
    
    /**
     * Ungroup selected group
     */
    ungroupSelected() {
        const activeObject = this.canvas.getActiveObject();
        if (activeObject && activeObject.type === 'group') {
            const objects = activeObject.getObjects();
            activeObject.destroy();
            this.canvas.remove(activeObject);
            
            objects.forEach(obj => {
                this.canvas.add(obj);
            });
            
            this.canvas.renderAll();
            this.emit('objects:ungrouped', { objects });
        }
    }
    
    /**
     * Save canvas state for undo/redo
     */
    saveState() {
        if (!this.stateful) return;
        
        const state = JSON.stringify(this.canvas.toJSON(['id', 'type', 'createdAt']));
        
        // Remove states after current index
        this.canvasHistory = this.canvasHistory.slice(0, this.historyIndex + 1);
        
        // Add new state
        this.canvasHistory.push(state);
        this.historyIndex++;
        
        // Limit history size
        if (this.canvasHistory.length > this.maxHistorySize) {
            this.canvasHistory.shift();
            this.historyIndex--;
        }
        
        this.emit('state:saved', { 
            canUndo: this.canUndo(), 
            canRedo: this.canRedo() 
        });
    }
    
    /**
     * Undo last action
     */
    undo() {
        if (this.canUndo()) {
            this.historyIndex--;
            this.loadState(this.canvasHistory[this.historyIndex]);
            this.emit('state:undo', { 
                canUndo: this.canUndo(), 
                canRedo: this.canRedo() 
            });
        }
    }
    
    /**
     * Redo last undone action
     */
    redo() {
        if (this.canRedo()) {
            this.historyIndex++;
            this.loadState(this.canvasHistory[this.historyIndex]);
            this.emit('state:redo', { 
                canUndo: this.canUndo(), 
                canRedo: this.canRedo() 
            });
        }
    }
    
    /**
     * Check if undo is possible
     */
    canUndo() {
        return this.historyIndex > 0;
    }
    
    /**
     * Check if redo is possible
     */
    canRedo() {
        return this.historyIndex < this.canvasHistory.length - 1;
    }
    
    /**
     * Load canvas state
     */
    loadState(state) {
        this.stateful = false;
        this.canvas.loadFromJSON(state, () => {
            this.canvas.renderAll();
            this.stateful = true;
            this.emit('state:loaded');
        });
    }
    
    /**
     * Register object in manager
     */
    registerObject(obj) {
        if (obj.id) {
            this.objects.set(obj.id, obj);
        }
    }
    
    /**
     * Unregister object from manager
     */
    unregisterObject(obj) {
        if (obj.id) {
            this.objects.delete(obj.id);
        }
    }
    
    /**
     * Generate unique object ID
     */
    generateObjectId() {
        return `obj_${++this.objectCounter}_${Date.now()}`;
    }
    
    /**
     * Get object by ID
     */
    getObjectById(id) {
        return this.objects.get(id);
    }
    
    /**
     * Get all objects
     */
    getAllObjects() {
        return Array.from(this.objects.values());
    }
    
    /**
     * Get selected objects
     */
    getSelectedObjects() {
        return this.selectedObjects;
    }
    
    /**
     * Export canvas as image
     */
    exportAsImage(options = {}) {
        const defaultOptions = {
            format: 'png',
            quality: 1,
            multiplier: 1,
            left: 0,
            top: 0,
            width: this.canvas.width,
            height: this.canvas.height
        };
        
        const exportOptions = { ...defaultOptions, ...options };
        
        return this.canvas.toDataURL(exportOptions);
    }
    
    /**
     * Export canvas as JSON
     */
    exportAsJSON() {
        return this.canvas.toJSON(['id', 'type', 'createdAt']);
    }
    
    /**
     * Import from JSON
     */
    importFromJSON(json) {
        return new Promise((resolve) => {
            this.canvas.loadFromJSON(json, () => {
                this.canvas.renderAll();
                this.saveState();
                this.emit('canvas:imported');
                resolve();
            });
        });
    }
    
    /**
     * Event emitter
     */
    emit(eventName, data = {}) {
        const event = new CustomEvent(`canvas:${eventName}`, {
            detail: { ...data, canvas: this.canvas, manager: this }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * Event listener
     */
    on(eventName, handler) {
        document.addEventListener(`canvas:${eventName}`, handler);
    }
    
    /**
     * Remove event listener
     */
    off(eventName, handler) {
        document.removeEventListener(`canvas:${eventName}`, handler);
    }
}

// Export for global use
window.MobileCanvasManager = MobileCanvasManager;
